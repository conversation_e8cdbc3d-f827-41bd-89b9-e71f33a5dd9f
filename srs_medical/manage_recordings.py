#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SRS医疗录制系统管理工具
提供命令行接口来管理录制会话和文件
"""

import os
import sys
import json
import argparse
import sqlite3
import requests
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict

class RecordingCLI:
    """录制管理命令行工具"""
    
    def __init__(self):
        self.db_path = "/data/MedicalRecord/srs_medical/recordings.db"
        self.api_base = "http://localhost:8888/api/v1"
        self.srs_api = "http://localhost:1985/api/v1"
    
    def list_sessions(self, status: str = None) -> List[Dict]:
        """列出录制会话"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if status:
                cursor.execute('''
                    SELECT * FROM recording_sessions 
                    WHERE status = ? 
                    ORDER BY created_at DESC
                ''', (status,))
            else:
                cursor.execute('''
                    SELECT * FROM recording_sessions 
                    ORDER BY created_at DESC
                ''')
            
            rows = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            sessions = [dict(zip(columns, row)) for row in rows]
            
            conn.close()
            return sessions
            
        except Exception as e:
            print(f"错误: 获取会话列表失败 - {e}")
            return []
    
    def get_session_details(self, session_id: str) -> Dict:
        """获取会话详情"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取会话信息
            cursor.execute('''
                SELECT * FROM recording_sessions WHERE session_id = ?
            ''', (session_id,))
            
            row = cursor.fetchone()
            if not row:
                return {}
            
            columns = [desc[0] for desc in cursor.description]
            session = dict(zip(columns, row))
            
            # 获取相关文件
            cursor.execute('''
                SELECT * FROM recording_files WHERE session_id = ?
            ''', (session_id,))
            
            file_rows = cursor.fetchall()
            file_columns = [desc[0] for desc in cursor.description]
            files = [dict(zip(file_columns, file_row)) for file_row in file_rows]
            
            session['files'] = files
            conn.close()
            
            return session
            
        except Exception as e:
            print(f"错误: 获取会话详情失败 - {e}")
            return {}
    
    def delete_session(self, session_id: str, delete_files: bool = False):
        """删除录制会话"""
        try:
            session = self.get_session_details(session_id)
            if not session:
                print(f"错误: 会话 {session_id} 不存在")
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 删除文件（如果指定）
            if delete_files and session.get('files'):
                for file_info in session['files']:
                    file_path = file_info['file_path']
                    if os.path.exists(file_path):
                        try:
                            os.remove(file_path)
                            print(f"已删除文件: {file_path}")
                        except Exception as e:
                            print(f"删除文件失败: {file_path} - {e}")
            
            # 删除数据库记录
            cursor.execute('DELETE FROM recording_files WHERE session_id = ?', (session_id,))
            cursor.execute('DELETE FROM recording_sessions WHERE session_id = ?', (session_id,))
            
            conn.commit()
            conn.close()
            
            print(f"会话 {session_id} 已删除")
            
        except Exception as e:
            print(f"错误: 删除会话失败 - {e}")
    
    def get_srs_streams(self) -> List[Dict]:
        """获取SRS当前流信息"""
        try:
            response = requests.get(f"{self.srs_api}/streams", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return data.get('streams', [])
            else:
                print(f"错误: SRS API请求失败 - {response.status_code}")
                return []
        except Exception as e:
            print(f"错误: 连接SRS API失败 - {e}")
            return []
    
    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计会话数量
            cursor.execute('SELECT status, COUNT(*) FROM recording_sessions GROUP BY status')
            status_counts = dict(cursor.fetchall())
            
            # 统计总录制时长
            cursor.execute('SELECT SUM(duration) FROM recording_sessions WHERE status = "completed"')
            total_duration = cursor.fetchone()[0] or 0
            
            # 统计文件大小
            cursor.execute('SELECT SUM(file_size) FROM recording_sessions WHERE status = "completed"')
            total_size = cursor.fetchone()[0] or 0
            
            # 最近24小时的会话
            yesterday = datetime.now() - timedelta(days=1)
            cursor.execute('''
                SELECT COUNT(*) FROM recording_sessions 
                WHERE created_at > ?
            ''', (yesterday,))
            recent_sessions = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'session_counts': status_counts,
                'total_duration': total_duration,
                'total_size': total_size,
                'recent_sessions': recent_sessions
            }
            
        except Exception as e:
            print(f"错误: 获取统计信息失败 - {e}")
            return {}
    
    def cleanup_old_files(self, days: int = 30):
        """清理过期文件"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查找过期会话
            cursor.execute('''
                SELECT session_id, file_path FROM recording_sessions 
                WHERE created_at < ? AND status = 'completed'
            ''', (cutoff_date,))
            
            old_sessions = cursor.fetchall()
            deleted_count = 0
            
            for session_id, file_path in old_sessions:
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                        print(f"已删除过期文件: {file_path}")
                    except Exception as e:
                        print(f"删除文件失败: {file_path} - {e}")
                
                # 删除数据库记录
                cursor.execute('DELETE FROM recording_files WHERE session_id = ?', (session_id,))
                cursor.execute('DELETE FROM recording_sessions WHERE session_id = ?', (session_id,))
            
            conn.commit()
            conn.close()
            
            print(f"清理完成，删除了 {deleted_count} 个过期文件")
            
        except Exception as e:
            print(f"错误: 清理过期文件失败 - {e}")

def format_duration(seconds):
    """格式化时长"""
    if not seconds:
        return "0秒"
    
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    secs = seconds % 60
    
    if hours > 0:
        return f"{hours}小时{minutes}分{secs}秒"
    elif minutes > 0:
        return f"{minutes}分{secs}秒"
    else:
        return f"{secs}秒"

def format_size(bytes_size):
    """格式化文件大小"""
    if not bytes_size:
        return "0 B"
    
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_size < 1024:
            return f"{bytes_size:.1f} {unit}"
        bytes_size /= 1024
    return f"{bytes_size:.1f} TB"

def main():
    parser = argparse.ArgumentParser(description='SRS医疗录制系统管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 列出会话
    list_parser = subparsers.add_parser('list', help='列出录制会话')
    list_parser.add_argument('--status', choices=['active', 'completed', 'failed'], 
                           help='按状态过滤')
    list_parser.add_argument('--limit', type=int, default=20, help='显示数量限制')
    
    # 会话详情
    detail_parser = subparsers.add_parser('detail', help='查看会话详情')
    detail_parser.add_argument('session_id', help='会话ID')
    
    # 删除会话
    delete_parser = subparsers.add_parser('delete', help='删除录制会话')
    delete_parser.add_argument('session_id', help='会话ID')
    delete_parser.add_argument('--delete-files', action='store_true', 
                             help='同时删除录制文件')
    
    # 当前流状态
    streams_parser = subparsers.add_parser('streams', help='查看当前流状态')
    
    # 系统统计
    stats_parser = subparsers.add_parser('stats', help='查看系统统计')
    
    # 清理过期文件
    cleanup_parser = subparsers.add_parser('cleanup', help='清理过期文件')
    cleanup_parser.add_argument('--days', type=int, default=30, 
                               help='清理多少天前的文件')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = RecordingCLI()
    
    if args.command == 'list':
        sessions = cli.list_sessions(args.status)
        if not sessions:
            print("没有找到录制会话")
            return
        
        print(f"{'会话ID':<30} {'应用':<10} {'流名称':<20} {'状态':<10} {'时长':<15} {'创建时间':<20}")
        print("-" * 110)
        
        for session in sessions[:args.limit]:
            duration = format_duration(session.get('duration', 0))
            created_at = session.get('created_at', '')[:19]
            print(f"{session['session_id']:<30} {session['app']:<10} {session['stream']:<20} "
                  f"{session['status']:<10} {duration:<15} {created_at:<20}")
    
    elif args.command == 'detail':
        session = cli.get_session_details(args.session_id)
        if not session:
            print(f"会话 {args.session_id} 不存在")
            return
        
        print(f"会话ID: {session['session_id']}")
        print(f"应用: {session['app']}")
        print(f"流名称: {session['stream']}")
        print(f"客户端IP: {session.get('client_ip', 'N/A')}")
        print(f"状态: {session['status']}")
        print(f"开始时间: {session.get('start_time', 'N/A')}")
        print(f"结束时间: {session.get('end_time', 'N/A')}")
        print(f"持续时间: {format_duration(session.get('duration', 0))}")
        print(f"文件大小: {format_size(session.get('file_size', 0))}")
        print(f"文件路径: {session.get('file_path', 'N/A')}")
        
        if session.get('files'):
            print("\n相关文件:")
            for file_info in session['files']:
                print(f"  - {file_info['file_path']} ({format_size(file_info['file_size'])})")
    
    elif args.command == 'delete':
        cli.delete_session(args.session_id, args.delete_files)
    
    elif args.command == 'streams':
        streams = cli.get_srs_streams()
        if not streams:
            print("当前没有活跃的流")
            return
        
        print(f"{'应用':<15} {'流名称':<25} {'客户端':<20} {'时长':<15}")
        print("-" * 80)
        
        for stream in streams:
            app = stream.get('app', 'N/A')
            name = stream.get('name', 'N/A')
            client = stream.get('client', {}).get('ip', 'N/A')
            duration = format_duration(stream.get('duration', 0))
            print(f"{app:<15} {name:<25} {client:<20} {duration:<15}")
    
    elif args.command == 'stats':
        stats = cli.get_system_stats()
        if not stats:
            print("无法获取统计信息")
            return
        
        print("=== 系统统计信息 ===")
        print(f"会话状态统计:")
        for status, count in stats['session_counts'].items():
            print(f"  {status}: {count}")
        
        print(f"\n总录制时长: {format_duration(stats['total_duration'])}")
        print(f"总文件大小: {format_size(stats['total_size'])}")
        print(f"最近24小时会话: {stats['recent_sessions']}")
    
    elif args.command == 'cleanup':
        cli.cleanup_old_files(args.days)

if __name__ == '__main__':
    main()
