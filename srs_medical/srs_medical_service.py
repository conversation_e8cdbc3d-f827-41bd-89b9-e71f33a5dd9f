#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SRS医疗录制系统服务管理器
提供完整的服务启动、停止、重启和状态监控功能
"""

import os
import sys
import time
import signal
import subprocess
import psutil
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/data/MedicalRecord/srs_medical/logs/service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SRSMedicalService:
    """SRS医疗录制服务管理器"""
    
    def __init__(self, base_dir: str = "/data/MedicalRecord"):
        self.base_dir = Path(base_dir)
        self.srs_medical_dir = self.base_dir / "srs_medical"
        self.srs_dir = self.base_dir / "srs" / "trunk"
        
        # 服务配置
        self.services = {
            'srs': {
                'name': 'SRS流媒体服务器',
                'executable': str(self.srs_dir / "objs" / "srs"),
                'config': str(self.srs_medical_dir / "medical_webrtc_recording.conf"),
                'pid_file': str(self.srs_medical_dir / "srs.pid"),
                'log_file': str(self.srs_medical_dir / "logs" / "srs.log"),
                'required': True
            },
            'recording_manager': {
                'name': '录制管理服务',
                'executable': str(self.srs_medical_dir / "recording_manager.py"),
                'config': str(self.srs_medical_dir / "recording_config.json"),
                'pid_file': str(self.srs_medical_dir / "recording_manager.pid"),
                'log_file': str(self.srs_medical_dir / "logs" / "recording_manager.log"),
                'required': True
            },
            'performance_monitor': {
                'name': '性能监控服务',
                'executable': str(self.srs_medical_dir / "performance_monitor.py"),
                'config': None,
                'pid_file': str(self.srs_medical_dir / "performance_monitor.pid"),
                'log_file': str(self.srs_medical_dir / "logs" / "performance_monitor.log"),
                'required': False
            }
        }
        
        # 确保目录存在
        self.srs_medical_dir.mkdir(exist_ok=True)
        (self.srs_medical_dir / "logs").mkdir(exist_ok=True)
        (self.srs_medical_dir / "recordings").mkdir(exist_ok=True)
    
    def check_prerequisites(self) -> Tuple[bool, List[str]]:
        """检查服务启动前提条件"""
        errors = []
        
        # 检查SRS可执行文件
        srs_executable = Path(self.services['srs']['executable'])
        if not srs_executable.exists():
            errors.append(f"SRS可执行文件不存在: {srs_executable}")
        elif not os.access(srs_executable, os.X_OK):
            errors.append(f"SRS可执行文件无执行权限: {srs_executable}")
        
        # 检查配置文件
        for service_name, service_config in self.services.items():
            if service_config['config']:
                config_file = Path(service_config['config'])
                if not config_file.exists():
                    errors.append(f"{service_config['name']}配置文件不存在: {config_file}")
        
        # 检查Python脚本
        for service_name in ['recording_manager', 'performance_monitor']:
            script_file = Path(self.services[service_name]['executable'])
            if not script_file.exists():
                errors.append(f"{self.services[service_name]['name']}脚本不存在: {script_file}")
            elif not os.access(script_file, os.X_OK):
                errors.append(f"{self.services[service_name]['name']}脚本无执行权限: {script_file}")
        
        # 检查网络端口
        required_ports = [1935, 8000, 8080, 1985, 8888]
        for port in required_ports:
            if self.is_port_in_use(port):
                errors.append(f"端口 {port} 已被占用")
        
        # 检查磁盘空间
        disk_usage = psutil.disk_usage(str(self.srs_medical_dir))
        free_gb = disk_usage.free / (1024**3)
        if free_gb < 1:  # 至少1GB空闲空间
            errors.append(f"磁盘空间不足，剩余: {free_gb:.1f}GB")
        
        return len(errors) == 0, errors
    
    def is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            connections = psutil.net_connections()
            for conn in connections:
                if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                    return True
            return False
        except Exception:
            return False
    
    def get_service_pid(self, service_name: str) -> Optional[int]:
        """获取服务进程ID"""
        try:
            pid_file = Path(self.services[service_name]['pid_file'])
            if pid_file.exists():
                with open(pid_file, 'r') as f:
                    pid = int(f.read().strip())
                
                # 验证进程是否存在
                if psutil.pid_exists(pid):
                    return pid
                else:
                    # 清理无效的PID文件
                    pid_file.unlink()
                    return None
            return None
        except Exception as e:
            logger.error(f"获取服务PID失败: {service_name} - {e}")
            return None
    
    def is_service_running(self, service_name: str) -> bool:
        """检查服务是否运行"""
        pid = self.get_service_pid(service_name)
        return pid is not None
    
    def start_service(self, service_name: str) -> bool:
        """启动单个服务"""
        if service_name not in self.services:
            logger.error(f"未知服务: {service_name}")
            return False
        
        service_config = self.services[service_name]
        
        # 检查服务是否已运行
        if self.is_service_running(service_name):
            logger.info(f"{service_config['name']}已在运行")
            return True
        
        try:
            logger.info(f"启动{service_config['name']}...")
            
            # 设置环境变量
            env = os.environ.copy()
            if service_name == 'srs':
                # 检测网络IP
                candidate_ip = self.detect_candidate_ip()
                env['CANDIDATE'] = candidate_ip
                logger.info(f"设置CANDIDATE IP: {candidate_ip}")
            
            # 构建启动命令
            if service_name == 'srs':
                cmd = [
                    service_config['executable'],
                    '-c', service_config['config']
                ]
            else:
                cmd = [
                    'python3',
                    service_config['executable']
                ]
                
                if service_name == 'recording_manager':
                    cmd.extend(['--config', service_config['config']])
                elif service_name == 'performance_monitor':
                    cmd.extend(['start'])
            
            # 启动进程
            with open(service_config['log_file'], 'a') as log_file:
                process = subprocess.Popen(
                    cmd,
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    env=env,
                    cwd=str(self.srs_medical_dir)
                )
            
            # 保存PID
            with open(service_config['pid_file'], 'w') as f:
                f.write(str(process.pid))
            
            # 等待服务启动
            time.sleep(2)
            
            # 验证服务是否成功启动
            if self.is_service_running(service_name):
                logger.info(f"{service_config['name']}启动成功 (PID: {process.pid})")
                return True
            else:
                logger.error(f"{service_config['name']}启动失败")
                return False
                
        except Exception as e:
            logger.error(f"启动{service_config['name']}失败: {e}")
            return False
    
    def stop_service(self, service_name: str, force: bool = False) -> bool:
        """停止单个服务"""
        if service_name not in self.services:
            logger.error(f"未知服务: {service_name}")
            return False
        
        service_config = self.services[service_name]
        pid = self.get_service_pid(service_name)
        
        if pid is None:
            logger.info(f"{service_config['name']}未运行")
            return True
        
        try:
            logger.info(f"停止{service_config['name']} (PID: {pid})...")
            
            process = psutil.Process(pid)
            
            if force:
                # 强制终止
                process.kill()
            else:
                # 优雅停止
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=10)
                except psutil.TimeoutExpired:
                    logger.warning(f"{service_config['name']}未在10秒内停止，强制终止")
                    process.kill()
            
            # 清理PID文件
            pid_file = Path(service_config['pid_file'])
            if pid_file.exists():
                pid_file.unlink()
            
            logger.info(f"{service_config['name']}已停止")
            return True
            
        except psutil.NoSuchProcess:
            logger.info(f"{service_config['name']}进程已不存在")
            # 清理PID文件
            pid_file = Path(service_config['pid_file'])
            if pid_file.exists():
                pid_file.unlink()
            return True
        except Exception as e:
            logger.error(f"停止{service_config['name']}失败: {e}")
            return False
    
    def restart_service(self, service_name: str) -> bool:
        """重启单个服务"""
        logger.info(f"重启服务: {service_name}")
        
        # 停止服务
        if not self.stop_service(service_name):
            return False
        
        # 等待一下
        time.sleep(1)
        
        # 启动服务
        return self.start_service(service_name)
    
    def start_all_services(self) -> bool:
        """启动所有服务"""
        logger.info("启动所有医疗录制服务...")
        
        # 检查前提条件
        ok, errors = self.check_prerequisites()
        if not ok:
            logger.error("前提条件检查失败:")
            for error in errors:
                logger.error(f"  - {error}")
            return False
        
        # 按顺序启动服务
        service_order = ['srs', 'recording_manager', 'performance_monitor']
        
        for service_name in service_order:
            service_config = self.services[service_name]
            
            # 跳过非必需服务（如果启动失败）
            if not service_config['required']:
                try:
                    self.start_service(service_name)
                except Exception as e:
                    logger.warning(f"可选服务{service_config['name']}启动失败: {e}")
                continue
            
            # 启动必需服务
            if not self.start_service(service_name):
                logger.error(f"必需服务{service_config['name']}启动失败，停止启动流程")
                return False
        
        logger.info("所有服务启动完成")
        return True
    
    def stop_all_services(self, force: bool = False) -> bool:
        """停止所有服务"""
        logger.info("停止所有医疗录制服务...")
        
        # 按相反顺序停止服务
        service_order = ['performance_monitor', 'recording_manager', 'srs']
        success = True
        
        for service_name in service_order:
            if not self.stop_service(service_name, force):
                success = False
        
        if success:
            logger.info("所有服务已停止")
        else:
            logger.warning("部分服务停止失败")
        
        return success
    
    def get_service_status(self) -> Dict:
        """获取所有服务状态"""
        status = {}
        
        for service_name, service_config in self.services.items():
            pid = self.get_service_pid(service_name)
            is_running = pid is not None
            
            service_status = {
                'name': service_config['name'],
                'running': is_running,
                'pid': pid,
                'required': service_config['required']
            }
            
            if is_running:
                try:
                    process = psutil.Process(pid)
                    service_status.update({
                        'cpu_percent': process.cpu_percent(),
                        'memory_mb': process.memory_info().rss / 1024 / 1024,
                        'start_time': datetime.fromtimestamp(process.create_time()),
                        'status': process.status()
                    })
                except Exception:
                    service_status['status'] = 'unknown'
            
            status[service_name] = service_status
        
        return status
    
    def detect_candidate_ip(self) -> str:
        """检测候选IP地址"""
        try:
            # 获取默认网关接口的IP
            import socket
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "127.0.0.1"
    
    def create_systemd_service(self) -> bool:
        """创建systemd服务文件"""
        try:
            service_content = f"""[Unit]
Description=SRS Medical Recording System
After=network.target

[Service]
Type=forking
User=root
WorkingDirectory={self.srs_medical_dir}
ExecStart={sys.executable} {__file__} start
ExecStop={sys.executable} {__file__} stop
ExecReload={sys.executable} {__file__} restart
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
            
            service_file = Path("/etc/systemd/system/srs-medical.service")
            with open(service_file, 'w') as f:
                f.write(service_content)
            
            # 重新加载systemd
            subprocess.run(['systemctl', 'daemon-reload'], check=True)
            
            logger.info(f"systemd服务文件已创建: {service_file}")
            logger.info("使用以下命令管理服务:")
            logger.info("  sudo systemctl enable srs-medical")
            logger.info("  sudo systemctl start srs-medical")
            logger.info("  sudo systemctl status srs-medical")
            
            return True
            
        except Exception as e:
            logger.error(f"创建systemd服务失败: {e}")
            return False

def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(description='SRS医疗录制系统服务管理器')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 启动服务
    start_parser = subparsers.add_parser('start', help='启动服务')
    start_parser.add_argument('--service', help='指定服务名称')
    
    # 停止服务
    stop_parser = subparsers.add_parser('stop', help='停止服务')
    stop_parser.add_argument('--service', help='指定服务名称')
    stop_parser.add_argument('--force', action='store_true', help='强制停止')
    
    # 重启服务
    restart_parser = subparsers.add_parser('restart', help='重启服务')
    restart_parser.add_argument('--service', help='指定服务名称')
    
    # 服务状态
    status_parser = subparsers.add_parser('status', help='查看服务状态')
    
    # 检查前提条件
    check_parser = subparsers.add_parser('check', help='检查前提条件')
    
    # 创建systemd服务
    install_parser = subparsers.add_parser('install', help='安装为系统服务')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    service_manager = SRSMedicalService()
    
    if args.command == 'start':
        if args.service:
            success = service_manager.start_service(args.service)
        else:
            success = service_manager.start_all_services()
        sys.exit(0 if success else 1)
    
    elif args.command == 'stop':
        if args.service:
            success = service_manager.stop_service(args.service, args.force)
        else:
            success = service_manager.stop_all_services(args.force)
        sys.exit(0 if success else 1)
    
    elif args.command == 'restart':
        if args.service:
            success = service_manager.restart_service(args.service)
        else:
            success = service_manager.stop_all_services()
            if success:
                time.sleep(2)
                success = service_manager.start_all_services()
        sys.exit(0 if success else 1)
    
    elif args.command == 'status':
        status = service_manager.get_service_status()
        
        print("=== SRS医疗录制系统服务状态 ===")
        for service_name, service_status in status.items():
            status_text = "运行中" if service_status['running'] else "已停止"
            required_text = "(必需)" if service_status['required'] else "(可选)"
            
            print(f"\n{service_status['name']} {required_text}: {status_text}")
            
            if service_status['running']:
                print(f"  PID: {service_status['pid']}")
                if 'cpu_percent' in service_status:
                    print(f"  CPU: {service_status['cpu_percent']:.1f}%")
                    print(f"  内存: {service_status['memory_mb']:.1f}MB")
                    print(f"  启动时间: {service_status['start_time']}")
    
    elif args.command == 'check':
        ok, errors = service_manager.check_prerequisites()
        
        if ok:
            print("✓ 所有前提条件检查通过")
        else:
            print("✗ 前提条件检查失败:")
            for error in errors:
                print(f"  - {error}")
        
        sys.exit(0 if ok else 1)
    
    elif args.command == 'install':
        success = service_manager.create_systemd_service()
        sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
