#!/bin/bash

# SRS医疗录制系统一键安装脚本
# 支持Ubuntu/Debian和CentOS/RHEL系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
BASE_DIR="/data/MedicalRecord"
SRS_MEDICAL_DIR="$BASE_DIR/srs_medical"
SRS_DIR="$BASE_DIR/srs/trunk"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# 检查系统类型
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    else
        OS=$(uname -s)
        VER=$(uname -r)
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本"
        exit 1
    fi
}

# 安装系统依赖
install_dependencies() {
    log_step "安装系统依赖"
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        apt update
        apt install -y build-essential git python3 python3-pip ffmpeg \
                      cmake pkg-config libssl-dev zlib1g-dev \
                      net-tools curl wget unzip
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        yum groupinstall -y "Development Tools"
        yum install -y git python3 python3-pip ffmpeg cmake \
                      openssl-devel zlib-devel net-tools curl wget unzip
        
        # CentOS可能需要EPEL源
        if ! rpm -qa | grep -q epel-release; then
            yum install -y epel-release
        fi
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    log_info "系统依赖安装完成"
}

# 安装Python依赖
install_python_deps() {
    log_step "安装Python依赖"
    
    pip3 install --upgrade pip
    pip3 install flask requests psutil
    
    log_info "Python依赖安装完成"
}

# 创建目录结构
create_directories() {
    log_step "创建目录结构"
    
    mkdir -p "$BASE_DIR"
    mkdir -p "$SRS_MEDICAL_DIR"
    mkdir -p "$SRS_MEDICAL_DIR/logs"
    mkdir -p "$SRS_MEDICAL_DIR/recordings"
    mkdir -p "$SRS_MEDICAL_DIR/www"
    
    log_info "目录结构创建完成"
}

# 下载和编译SRS
install_srs() {
    log_step "下载和编译SRS 6.0"
    
    if [ ! -d "$BASE_DIR/srs" ]; then
        cd "$BASE_DIR"
        git clone https://github.com/ossrs/srs.git
    fi
    
    cd "$SRS_DIR"
    
    # 检查是否已编译
    if [ -f "objs/srs" ]; then
        log_info "SRS已编译，跳过编译步骤"
        return
    fi
    
    # 配置编译选项
    ./configure --rtc=on --https=on --h265=on --srt=off --jobs=4
    
    # 编译
    make
    
    # 验证编译结果
    if [ -f "objs/srs" ]; then
        log_info "SRS编译成功"
        ./objs/srs -v
    else
        log_error "SRS编译失败"
        exit 1
    fi
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙"
    
    # 检查防火墙类型
    if command -v ufw >/dev/null 2>&1; then
        # Ubuntu UFW
        ufw allow 1935/tcp  # RTMP
        ufw allow 8000/udp  # WebRTC
        ufw allow 8080/tcp  # HTTP
        ufw allow 1985/tcp  # API
        ufw allow 8888/tcp  # Recording API
        log_info "UFW防火墙规则已配置"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        # CentOS firewalld
        firewall-cmd --permanent --add-port=1935/tcp
        firewall-cmd --permanent --add-port=8000/udp
        firewall-cmd --permanent --add-port=8080/tcp
        firewall-cmd --permanent --add-port=1985/tcp
        firewall-cmd --permanent --add-port=8888/tcp
        firewall-cmd --reload
        log_info "firewalld防火墙规则已配置"
    else
        log_warn "未检测到防火墙，请手动配置端口: 1935,8000,8080,1985,8888"
    fi
}

# 设置文件权限
set_permissions() {
    log_step "设置文件权限"
    
    # 设置目录权限
    chmod -R 755 "$SRS_MEDICAL_DIR"
    chmod -R 777 "$SRS_MEDICAL_DIR/logs"
    chmod -R 777 "$SRS_MEDICAL_DIR/recordings"
    
    # 设置脚本执行权限
    find "$SRS_MEDICAL_DIR" -name "*.py" -exec chmod +x {} \;
    find "$SRS_MEDICAL_DIR" -name "*.sh" -exec chmod +x {} \;
    
    log_info "文件权限设置完成"
}

# 初始化配置
initialize_config() {
    log_step "初始化配置"
    
    cd "$SRS_MEDICAL_DIR"
    
    # 运行环境配置脚本
    if [ -f "setup_env.sh" ]; then
        ./setup_env.sh
        log_info "环境配置完成"
    else
        log_warn "setup_env.sh不存在，请手动配置环境"
    fi
}

# 安装系统服务
install_system_service() {
    log_step "安装系统服务"
    
    if [ -f "$SRS_MEDICAL_DIR/srs_medical_service.py" ]; then
        python3 "$SRS_MEDICAL_DIR/srs_medical_service.py" install
        log_info "系统服务安装完成"
        
        # 启用服务
        systemctl enable srs-medical
        log_info "系统服务已启用"
    else
        log_warn "服务管理器不存在，跳过系统服务安装"
    fi
}

# 运行测试
run_tests() {
    log_step "运行系统测试"
    
    # 启动服务
    if [ -f "$SRS_MEDICAL_DIR/srs_medical.sh" ]; then
        "$SRS_MEDICAL_DIR/srs_medical.sh" start
        
        # 等待服务启动
        sleep 10
        
        # 运行测试
        if [ -f "$SRS_MEDICAL_DIR/test_system.py" ]; then
            python3 "$SRS_MEDICAL_DIR/test_system.py" --test-duration 10
            log_info "系统测试完成"
        else
            log_warn "测试脚本不存在，跳过测试"
        fi
    else
        log_warn "管理脚本不存在，请手动启动服务"
    fi
}

# 显示安装结果
show_results() {
    log_step "安装完成"
    
    # 获取IP地址
    IP=$(hostname -I | awk '{print $1}')
    
    echo -e "${GREEN}"
    cat << EOF
╔══════════════════════════════════════════════════════════════╗
║                SRS医疗录制系统安装完成                        ║
╠══════════════════════════════════════════════════════════════╣
║                                                              ║
║  访问地址:                                                   ║
║    WebRTC测试页面: http://$IP:8080                    ║
║    SRS管理控制台:  http://$IP:8080/console           ║
║    API接口文档:    http://$IP:1985/api/v1            ║
║                                                              ║
║  管理命令:                                                   ║
║    启动服务: $SRS_MEDICAL_DIR/srs_medical.sh start          ║
║    停止服务: $SRS_MEDICAL_DIR/srs_medical.sh stop           ║
║    查看状态: $SRS_MEDICAL_DIR/srs_medical.sh status         ║
║    查看日志: $SRS_MEDICAL_DIR/srs_medical.sh logs srs       ║
║                                                              ║
║  系统服务:                                                   ║
║    启动: sudo systemctl start srs-medical                   ║
║    停止: sudo systemctl stop srs-medical                    ║
║    状态: sudo systemctl status srs-medical                  ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
    
    log_info "安装日志已保存到: /var/log/srs_medical_install.log"
    log_info "详细文档请查看: $SRS_MEDICAL_DIR/README.md"
}

# 清理函数
cleanup() {
    if [ $? -ne 0 ]; then
        log_error "安装过程中出现错误"
        log_info "请检查日志文件: /var/log/srs_medical_install.log"
        log_info "或运行以下命令获取帮助:"
        log_info "  $SRS_MEDICAL_DIR/srs_medical.sh check"
    fi
}

# 主安装函数
main() {
    # 设置错误处理
    trap cleanup EXIT
    
    # 重定向输出到日志文件
    exec > >(tee -a /var/log/srs_medical_install.log)
    exec 2>&1
    
    log_info "开始安装SRS医疗录制系统 - $(date)"
    
    # 检查权限
    check_permissions
    
    # 检测操作系统
    detect_os
    
    # 安装步骤
    install_dependencies
    install_python_deps
    create_directories
    install_srs
    configure_firewall
    set_permissions
    initialize_config
    install_system_service
    run_tests
    
    # 显示结果
    show_results
    
    log_info "安装完成 - $(date)"
}

# 显示帮助信息
show_help() {
    cat << EOF
SRS医疗录制系统一键安装脚本

用法: $0 [选项]

选项:
  --help, -h          显示此帮助信息
  --skip-tests        跳过系统测试
  --skip-firewall     跳过防火墙配置
  --skip-service      跳过系统服务安装
  --base-dir DIR      指定安装目录 (默认: /data/MedicalRecord)

示例:
  $0                  # 完整安装
  $0 --skip-tests     # 安装但跳过测试
  $0 --base-dir /opt/srs-medical  # 安装到指定目录

EOF
}

# 解析命令行参数
SKIP_TESTS=false
SKIP_FIREWALL=false
SKIP_SERVICE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            show_help
            exit 0
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-firewall)
            SKIP_FIREWALL=true
            shift
            ;;
        --skip-service)
            SKIP_SERVICE=true
            shift
            ;;
        --base-dir)
            BASE_DIR="$2"
            SRS_MEDICAL_DIR="$BASE_DIR/srs_medical"
            SRS_DIR="$BASE_DIR/srs/trunk"
            shift 2
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 根据参数调整安装流程
if [ "$SKIP_TESTS" = true ]; then
    run_tests() { log_info "跳过系统测试"; }
fi

if [ "$SKIP_FIREWALL" = true ]; then
    configure_firewall() { log_info "跳过防火墙配置"; }
fi

if [ "$SKIP_SERVICE" = true ]; then
    install_system_service() { log_info "跳过系统服务安装"; }
fi

# 执行主函数
main
