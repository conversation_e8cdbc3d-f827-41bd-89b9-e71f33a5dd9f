{"database": {"path": "/data/MedicalRecord/srs_medical/recordings.db"}, "recordings": {"base_dir": "/data/MedicalRecord/srs_medical/recordings", "max_duration": 7200, "cleanup_days": 30, "formats": ["mp4", "flv"], "quality": {"video": {"codec": "h264", "bitrate": "2000k", "fps": 25, "resolution": "1280x720"}, "audio": {"codec": "aac", "bitrate": "128k", "sample_rate": 44100, "channels": 2}}}, "srs": {"api_url": "http://localhost:1985/api/v1", "rtmp_port": 1935, "http_port": 8080, "rtc_port": 8000}, "webhook": {"host": "0.0.0.0", "port": 8888, "endpoints": {"on_publish": "/api/v1/streams/on_publish", "on_unpublish": "/api/v1/streams/on_unpublish", "on_play": "/api/v1/streams/on_play", "on_stop": "/api/v1/streams/on_stop", "on_dvr": "/api/v1/streams/on_dvr"}}, "monitoring": {"enabled": true, "check_interval": 30, "max_session_duration": 7200, "disk_space_threshold": 85}, "security": {"allowed_ips": ["127.0.0.1", "***********/16", "10.0.0.0/8"], "max_concurrent_sessions": 50, "session_timeout": 3600}, "logging": {"level": "INFO", "file": "/data/MedicalRecord/srs_medical/logs/recording_manager.log", "max_size": "100MB", "backup_count": 5}}