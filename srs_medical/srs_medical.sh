#!/bin/bash

# SRS医疗录制系统管理脚本
# 提供简单的命令行接口来管理整个系统

set -e

# 配置变量
BASE_DIR="/data/MedicalRecord"
SRS_MEDICAL_DIR="$BASE_DIR/srs_medical"
SERVICE_MANAGER="$SRS_MEDICAL_DIR/srs_medical_service.py"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Python3
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查必要的Python包
    python3 -c "import flask, requests, psutil" 2>/dev/null || {
        log_error "缺少必要的Python包，请运行: pip3 install flask requests psutil"
        exit 1
    }
    
    # 检查服务管理器
    if [ ! -f "$SERVICE_MANAGER" ]; then
        log_error "服务管理器不存在: $SERVICE_MANAGER"
        exit 1
    fi
    
    # 检查权限
    if [ ! -x "$SERVICE_MANAGER" ]; then
        log_warn "服务管理器无执行权限，正在修复..."
        chmod +x "$SERVICE_MANAGER"
    fi
    
    log_info "依赖检查完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
SRS医疗录制系统管理脚本

用法: $0 <命令> [选项]

命令:
  start           启动所有服务
  stop            停止所有服务
  restart         重启所有服务
  status          查看服务状态
  check           检查系统前提条件
  logs            查看日志
  install         安装为系统服务
  uninstall       卸载系统服务
  backup          备份配置和数据
  restore         恢复配置和数据
  update          更新系统
  monitor         实时监控
  help            显示此帮助信息

服务管理:
  start <service>     启动指定服务 (srs|recording_manager|performance_monitor)
  stop <service>      停止指定服务
  restart <service>   重启指定服务

日志查看:
  logs <service>      查看指定服务日志
  logs -f <service>   实时跟踪服务日志
  logs --error        查看错误日志

示例:
  $0 start                    # 启动所有服务
  $0 status                   # 查看状态
  $0 logs srs                 # 查看SRS日志
  $0 logs -f recording_manager # 实时跟踪录制管理器日志
  $0 restart srs              # 重启SRS服务

EOF
}

# 启动服务
start_services() {
    local service="$1"
    
    log_info "启动SRS医疗录制服务..."
    
    if [ -n "$service" ]; then
        python3 "$SERVICE_MANAGER" start --service "$service"
    else
        python3 "$SERVICE_MANAGER" start
    fi
    
    if [ $? -eq 0 ]; then
        log_info "服务启动成功"
        show_quick_status
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 停止服务
stop_services() {
    local service="$1"
    local force="$2"
    
    log_info "停止SRS医疗录制服务..."
    
    local cmd="python3 $SERVICE_MANAGER stop"
    if [ -n "$service" ]; then
        cmd="$cmd --service $service"
    fi
    if [ "$force" = "force" ]; then
        cmd="$cmd --force"
    fi
    
    eval "$cmd"
    
    if [ $? -eq 0 ]; then
        log_info "服务停止成功"
    else
        log_error "服务停止失败"
        exit 1
    fi
}

# 重启服务
restart_services() {
    local service="$1"
    
    log_info "重启SRS医疗录制服务..."
    
    if [ -n "$service" ]; then
        python3 "$SERVICE_MANAGER" restart --service "$service"
    else
        python3 "$SERVICE_MANAGER" restart
    fi
    
    if [ $? -eq 0 ]; then
        log_info "服务重启成功"
        show_quick_status
    else
        log_error "服务重启失败"
        exit 1
    fi
}

# 查看服务状态
show_status() {
    python3 "$SERVICE_MANAGER" status
}

# 快速状态显示
show_quick_status() {
    echo
    log_info "服务状态概览:"
    python3 "$SERVICE_MANAGER" status | grep -E "(运行中|已停止)" | head -3
    echo
    log_info "访问地址:"
    echo "  WebRTC测试页面: http://$(hostname -I | awk '{print $1}'):8080"
    echo "  SRS管理界面:   http://$(hostname -I | awk '{print $1}'):8080/console"
    echo "  API接口:       http://$(hostname -I | awk '{print $1}'):1985/api/v1"
    echo
}

# 检查前提条件
check_prerequisites() {
    python3 "$SERVICE_MANAGER" check
}

# 查看日志
show_logs() {
    local service="$1"
    local follow="$2"
    
    if [ "$service" = "--error" ]; then
        # 显示错误日志
        log_info "查看错误日志..."
        find "$SRS_MEDICAL_DIR/logs" -name "*.log" -exec grep -l "ERROR\|CRITICAL\|Exception" {} \; | while read logfile; do
            echo -e "\n${YELLOW}=== $(basename "$logfile") ===${NC}"
            grep -n "ERROR\|CRITICAL\|Exception" "$logfile" | tail -10
        done
        return
    fi
    
    if [ -z "$service" ]; then
        log_error "请指定服务名称: srs, recording_manager, performance_monitor"
        exit 1
    fi
    
    local log_file="$SRS_MEDICAL_DIR/logs/${service}.log"
    
    if [ ! -f "$log_file" ]; then
        log_error "日志文件不存在: $log_file"
        exit 1
    fi
    
    if [ "$follow" = "-f" ]; then
        log_info "实时跟踪 $service 日志 (Ctrl+C 退出)..."
        tail -f "$log_file"
    else
        log_info "显示 $service 最近日志..."
        tail -50 "$log_file"
    fi
}

# 安装系统服务
install_service() {
    log_info "安装SRS医疗录制系统为系统服务..."
    
    if [ "$EUID" -ne 0 ]; then
        log_error "需要root权限来安装系统服务"
        exit 1
    fi
    
    python3 "$SERVICE_MANAGER" install
    
    if [ $? -eq 0 ]; then
        log_info "系统服务安装成功"
        log_info "使用以下命令管理服务:"
        echo "  sudo systemctl enable srs-medical"
        echo "  sudo systemctl start srs-medical"
        echo "  sudo systemctl status srs-medical"
    else
        log_error "系统服务安装失败"
        exit 1
    fi
}

# 卸载系统服务
uninstall_service() {
    log_info "卸载SRS医疗录制系统服务..."
    
    if [ "$EUID" -ne 0 ]; then
        log_error "需要root权限来卸载系统服务"
        exit 1
    fi
    
    # 停止并禁用服务
    systemctl stop srs-medical 2>/dev/null || true
    systemctl disable srs-medical 2>/dev/null || true
    
    # 删除服务文件
    rm -f /etc/systemd/system/srs-medical.service
    
    # 重新加载systemd
    systemctl daemon-reload
    
    log_info "系统服务卸载完成"
}

# 备份配置和数据
backup_system() {
    local backup_dir="/tmp/srs_medical_backup_$(date +%Y%m%d_%H%M%S)"
    
    log_info "备份SRS医疗录制系统到: $backup_dir"
    
    mkdir -p "$backup_dir"
    
    # 备份配置文件
    cp -r "$SRS_MEDICAL_DIR"/*.conf "$backup_dir/" 2>/dev/null || true
    cp -r "$SRS_MEDICAL_DIR"/*.json "$backup_dir/" 2>/dev/null || true
    
    # 备份数据库
    cp -r "$SRS_MEDICAL_DIR"/*.db "$backup_dir/" 2>/dev/null || true
    
    # 备份脚本
    cp -r "$SRS_MEDICAL_DIR"/*.py "$backup_dir/" 2>/dev/null || true
    cp -r "$SRS_MEDICAL_DIR"/*.sh "$backup_dir/" 2>/dev/null || true
    
    # 创建备份信息文件
    cat > "$backup_dir/backup_info.txt" << EOF
备份时间: $(date)
系统版本: $(uname -a)
备份内容: 配置文件、数据库、脚本
原始路径: $SRS_MEDICAL_DIR
EOF
    
    # 压缩备份
    tar -czf "${backup_dir}.tar.gz" -C "$(dirname "$backup_dir")" "$(basename "$backup_dir")"
    rm -rf "$backup_dir"
    
    log_info "备份完成: ${backup_dir}.tar.gz"
}

# 实时监控
monitor_system() {
    log_info "启动实时监控 (Ctrl+C 退出)..."
    
    while true; do
        clear
        echo -e "${GREEN}=== SRS医疗录制系统实时监控 ===${NC}"
        echo "更新时间: $(date)"
        echo
        
        # 显示服务状态
        python3 "$SERVICE_MANAGER" status
        
        echo
        echo -e "${BLUE}=== 系统资源 ===${NC}"
        
        # CPU和内存使用率
        echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
        echo "内存使用率: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')"
        
        # 磁盘使用率
        echo "录制目录磁盘使用率: $(df "$SRS_MEDICAL_DIR" | tail -1 | awk '{print $5}')"
        
        # 网络连接
        echo "活跃连接数: $(netstat -an | grep ESTABLISHED | wc -l)"
        
        echo
        echo "按 Ctrl+C 退出监控"
        
        sleep 5
    done
}

# 主函数
main() {
    # 检查基本依赖
    check_dependencies
    
    case "$1" in
        start)
            start_services "$2"
            ;;
        stop)
            if [ "$2" = "--force" ] || [ "$3" = "--force" ]; then
                stop_services "$2" "force"
            else
                stop_services "$2"
            fi
            ;;
        restart)
            restart_services "$2"
            ;;
        status)
            show_status
            ;;
        check)
            check_prerequisites
            ;;
        logs)
            if [ "$2" = "-f" ]; then
                show_logs "$3" "-f"
            else
                show_logs "$2" "$3"
            fi
            ;;
        install)
            install_service
            ;;
        uninstall)
            uninstall_service
            ;;
        backup)
            backup_system
            ;;
        monitor)
            monitor_system
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'echo -e "\n${YELLOW}操作已取消${NC}"; exit 130' INT

# 执行主函数
main "$@"
