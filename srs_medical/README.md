# SRS 6.0 医疗WebRTC录制系统

## 项目概述

本项目基于SRS 6.0流媒体服务器，为医疗行业提供专业的WebRTC实时流媒体录制解决方案。系统支持自动录制、会话管理、文件存储和性能监控等功能。

### 主要特性

- ✅ **WebRTC实时流媒体**: 支持浏览器直接推流和播放
- ✅ **自动录制**: 基于会话的自动录制，无需手动干预
- ✅ **高性能**: 支持多路并发流，优化的编码和网络配置
- ✅ **完整管理**: 提供录制管理、性能监控和系统维护工具
- ✅ **安全可靠**: 医疗级别的数据安全和文件完整性保障
- ✅ **易于部署**: 一键安装和配置，支持系统服务

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端客户端     │    │   SRS 6.0      │    │   录制管理器     │
│   (WebRTC)     │◄──►│   流媒体服务器   │◄──►│   (Python)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   DVR录制模块   │    │   SQLite数据库  │
                       │   (MP4文件)    │    │   (会话记录)    │
                       └─────────────────┘    └─────────────────┘
```

## 快速开始

### 1. 系统要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 100GB以上可用空间
- **网络**: 100Mbps以上带宽

### 2. 依赖安装

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y build-essential git python3 python3-pip ffmpeg

# CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y git python3 python3-pip ffmpeg

# Python依赖
pip3 install flask requests psutil
```

### 3. 快速部署

```bash
# 1. 进入项目目录
cd /data/MedicalRecord

# 2. 检查系统前提条件
./srs_medical/srs_medical.sh check

# 3. 启动所有服务
./srs_medical/srs_medical.sh start

# 4. 查看服务状态
./srs_medical/srs_medical.sh status
```

### 4. 访问系统

- **WebRTC测试页面**: http://YOUR_IP:8080
- **SRS管理控制台**: http://YOUR_IP:8080/console
- **API接口文档**: http://YOUR_IP:1985/api/v1

## 详细安装指南

### 步骤1: 编译SRS 6.0

```bash
cd /data/MedicalRecord/srs/trunk

# 配置编译选项
./configure --rtc=on --https=on --h265=on --srt=off --jobs=4

# 编译
make

# 验证编译结果
./objs/srs -v
```

### 步骤2: 配置环境

```bash
cd /data/MedicalRecord/srs_medical

# 运行环境配置脚本
./setup_env.sh

# 检查网络配置
echo $CANDIDATE
```

### 步骤3: 启动服务

```bash
# 使用服务管理器启动
python3 srs_medical_service.py start

# 或使用便捷脚本
./srs_medical.sh start
```

## 配置说明

### 主要配置文件

| 文件 | 说明 |
|------|------|
| `medical_webrtc_recording.conf` | SRS主配置文件 |
| `high_performance.conf` | 高性能配置文件 |
| `recording_config.json` | 录制管理器配置 |

### 关键配置项

#### SRS配置 (`medical_webrtc_recording.conf`)

```nginx
# WebRTC服务器
rtc_server {
    enabled         on;
    listen          8000;
    candidate       $CANDIDATE;
}

# DVR录制
dvr {
    enabled         on;
    dvr_plan        session;
    dvr_path        ../srs_medical/recordings/[app]/[2006]/[01]/[02]/[stream]_[timestamp].mp4;
}
```

#### 录制管理器配置 (`recording_config.json`)

```json
{
    "database": {
        "path": "/data/MedicalRecord/srs_medical/recordings.db"
    },
    "recording": {
        "base_path": "/data/MedicalRecord/srs_medical/recordings",
        "quality": "high",
        "auto_cleanup": true
    }
}
```

## 使用指南

### 基本操作

```bash
# 启动系统
./srs_medical.sh start

# 停止系统
./srs_medical.sh stop

# 重启系统
./srs_medical.sh restart

# 查看状态
./srs_medical.sh status

# 查看日志
./srs_medical.sh logs srs
./srs_medical.sh logs -f recording_manager
```

### 录制管理

```bash
# 查看录制会话
python3 manage_recordings.py list

# 查看会话详情
python3 manage_recordings.py detail SESSION_ID

# 删除会话
python3 manage_recordings.py delete SESSION_ID --delete-files

# 系统统计
python3 manage_recordings.py stats
```

### 存储管理

```bash
# 查看录制文件
python3 storage_manager.py list

# 存储统计
python3 storage_manager.py stats

# 归档旧文件
python3 storage_manager.py archive --days 30

# 清理空目录
python3 storage_manager.py cleanup
```

### 性能监控

```bash
# 启动性能监控
python3 performance_monitor.py start

# 查看性能摘要
python3 performance_monitor.py summary --hours 24

# 查看当前状态
python3 performance_monitor.py status
```

## API接口

### SRS API

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/v1/summaries` | GET | 系统概览 |
| `/api/v1/streams` | GET | 流列表 |
| `/api/v1/clients` | GET | 客户端列表 |

### 录制管理API

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/v1/sessions` | GET | 录制会话列表 |
| `/api/v1/sessions/{id}` | GET | 会话详情 |
| `/api/v1/stats` | GET | 统计信息 |
| `/api/v1/health` | GET | 健康检查 |

## 测试验证

### 运行系统测试

```bash
# 完整系统测试
python3 test_system.py

# 指定测试时长
python3 test_system.py --test-duration 60

# 查看测试报告
python3 test_system.py --report-only
```

### 手动测试步骤

1. **访问WebRTC页面**: http://YOUR_IP:8080
2. **允许摄像头和麦克风权限**
3. **点击"开始推流"按钮**
4. **观察录制状态和文件生成**
5. **停止推流并检查录制文件**

## 故障排除

### 常见问题

#### 1. 服务启动失败

```bash
# 检查端口占用
netstat -tlnp | grep -E "(1935|8000|8080|1985|8888)"

# 检查日志
./srs_medical.sh logs srs
./srs_medical.sh logs recording_manager
```

#### 2. WebRTC连接失败

```bash
# 检查CANDIDATE IP设置
echo $CANDIDATE

# 重新检测网络
./setup_env.sh

# 检查防火墙
sudo ufw status
sudo firewall-cmd --list-all
```

#### 3. 录制文件未生成

```bash
# 检查录制目录权限
ls -la /data/MedicalRecord/srs_medical/recordings/

# 检查磁盘空间
df -h /data/MedicalRecord/

# 查看DVR日志
grep -i dvr /data/MedicalRecord/srs_medical/logs/srs.log
```

#### 4. 性能问题

```bash
# 查看系统资源
top
free -h
iostat

# 查看性能监控
python3 performance_monitor.py status

# 使用高性能配置
cp high_performance.conf medical_webrtc_recording.conf
./srs_medical.sh restart
```

### 日志文件位置

| 服务 | 日志文件 |
|------|----------|
| SRS | `/data/MedicalRecord/srs_medical/logs/srs.log` |
| 录制管理器 | `/data/MedicalRecord/srs_medical/logs/recording_manager.log` |
| 性能监控 | `/data/MedicalRecord/srs_medical/logs/performance_monitor.log` |
| 服务管理器 | `/data/MedicalRecord/srs_medical/logs/service.log` |

## 系统维护

### 定期维护任务

```bash
# 每日任务
0 2 * * * /data/MedicalRecord/srs_medical/storage_manager.py cleanup

# 每周任务
0 3 * * 0 /data/MedicalRecord/srs_medical/storage_manager.py archive --days 7

# 每月任务
0 4 1 * * /data/MedicalRecord/srs_medical/srs_medical.sh backup
```

### 备份和恢复

```bash
# 创建备份
./srs_medical.sh backup

# 恢复配置
tar -xzf /tmp/srs_medical_backup_*.tar.gz
cp backup_dir/*.conf /data/MedicalRecord/srs_medical/
cp backup_dir/*.json /data/MedicalRecord/srs_medical/
```

### 系统升级

```bash
# 停止服务
./srs_medical.sh stop

# 备份当前配置
./srs_medical.sh backup

# 更新代码
git pull origin main

# 重新编译SRS（如需要）
cd /data/MedicalRecord/srs/trunk
make clean
./configure --rtc=on --https=on --h265=on --srt=off --jobs=4
make

# 启动服务
./srs_medical.sh start

# 验证功能
python3 test_system.py
```

## 安全建议

### 网络安全

1. **防火墙配置**:
   ```bash
   # 仅开放必要端口
   sudo ufw allow 1935/tcp  # RTMP
   sudo ufw allow 8000/udp  # WebRTC
   sudo ufw allow 8080/tcp  # HTTP
   sudo ufw allow 1985/tcp  # API
   ```

2. **SSL/TLS配置**: 生产环境建议启用HTTPS

3. **访问控制**: 配置IP白名单限制访问

### 数据安全

1. **文件权限**: 录制文件设置适当的访问权限
2. **数据加密**: 敏感数据建议加密存储
3. **定期备份**: 建立完善的备份策略

## 高级配置

### 集群部署

对于大规模部署，可以配置SRS集群：

```nginx
# 在high_performance.conf中配置
cluster {
    mode            local;
    origin_cluster  off;
    coworkers       127.0.0.1:1935 127.0.0.1:1936 127.0.0.1:1937;
    load_balance    on;
}
```

### 硬件加速

启用GPU硬件编码以提高性能：

```nginx
# 转码配置
transcode {
    enabled         on;
    engine medical_hd {
        vcodec          h264_nvenc;  # NVIDIA硬件编码
        vbitrate        2000;
        vfps            25;
        vwidth          1920;
        vheight         1080;
    }
}
```

### 自定义存储

配置自定义存储路径和命名规则：

```python
# 在storage_manager.py中修改
self.config = {
    "naming": {
        "timestamp_format": "%Y%m%d_%H%M%S",
        "include_hash": True
    },
    "structure": {
        "by_date": True,
        "by_app": True,
        "max_depth": 4
    }
}
```

## 监控和告警

### Prometheus集成

可以集成Prometheus进行监控：

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'srs-medical'
    static_configs:
      - targets: ['localhost:1985']
```

### 告警配置

配置告警通知：

```json
{
    "alerts": {
        "enabled": true,
        "webhook_url": "https://your-webhook-url.com/alerts",
        "email_enabled": true,
        "email_smtp": {
            "server": "smtp.example.com",
            "port": 587,
            "username": "<EMAIL>",
            "password": "your-password"
        }
    }
}
```

## 技术支持

### 联系方式

- **项目地址**: https://github.com/your-org/srs-medical
- **文档地址**: https://docs.your-org.com/srs-medical
- **问题反馈**: https://github.com/your-org/srs-medical/issues

### 版本信息

- **SRS版本**: 6.0.134
- **系统版本**: 1.0.0
- **更新日期**: 2025-07-30

### 更新日志

#### v1.0.0 (2025-07-30)
- ✅ 初始版本发布
- ✅ 完整的WebRTC录制功能
- ✅ 自动化部署和管理工具
- ✅ 性能监控和告警系统
- ✅ 完整的测试验证方案

---

© 2025 医疗录制系统项目组. 保留所有权利.
