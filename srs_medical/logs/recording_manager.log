2025-07-30 16:00:21,497 - __main__ - INFO - 数据库初始化完成
2025-07-30 16:00:21,497 - __main__ - INFO - 数据库初始化完成
2025-07-30 16:00:21,499 - __main__ - INFO - 启动录制管理服务器: 0.0.0.0:8888
2025-07-30 16:00:21,499 - __main__ - INFO - 启动录制管理服务器: 0.0.0.0:8888
 * Serving Flask app 'recording_manager'
 * Debug mode: off
2025-07-30 16:00:21,500 - __main__ - INFO - 清理完成，删除了 0 个过期录制文件
2025-07-30 16:00:21,500 - __main__ - INFO - 清理完成，删除了 0 个过期录制文件
2025-07-30 16:00:21,500 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8888
 * Running on http://**************:8888
2025-07-30 16:00:21,500 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8888
 * Running on http://**************:8888
2025-07-30 16:00:21,501 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 16:00:21,501 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
