[2025-07-30 16:00:19.295][INFO][369062][703e709l] XCORE-SRS/6.0.134(Hang)
[2025-07-30 16:00:19.296][INFO][369062][703e709l] config parse complete
[33m[2025-07-30 16:00:19.296][WARN][369062][703e709l][22] transform: vhost.refer to vhost.refer.all for vhost
[0m[2025-07-30 16:00:19.297][INFO][369062][703e709l] you can check log by: tail -n 30 -f ../srs_medical/logs/srs.log
[2025-07-30 16:00:19.297][INFO][369062][703e709l] please check SRS by: ./etc/init.d/srs status
[2025-07-30 16:00:19.297][INFO][369062][703e709l] SRS/6.0.134(<PERSON>), MIT
[2025-07-30 16:00:19.297][INFO][369062][703e709l] authors: <PERSON><PERSON><<EMAIL>> <PERSON><PERSON><PERSON><PERSON><<EMAIL>> <PERSON><PERSON><<EMAIL>> <PERSON><PERSON>enjie<<EMAIL>> ShiWei<<EMAIL>> XiaoZhihong<<EMAIL>> WuPengqiang<<EMAIL>> XiaLixin<<EMAIL>> LiPeng<<EMAIL>> ChenGuanghua<<EMAIL>> ChenHaibo<<EMAIL>> ZhangJunqin<<EMAIL>> and https://github.com/ossrs/srs/blob/develop/trunk/AUTHORS.md#contributors
[2025-07-30 16:00:19.297][INFO][369062][703e709l] cwd=/data/MedicalRecord/srs_medical, work_dir=./, build: 2025-07-30 15:10:07, configure: --rtc=on --https=on --h265=on --srt=off --jobs=4, uname: Linux gpuserver 5.4.0-216-generic #236-Ubuntu SMP Fri Apr 11 19:53:21 UTC 2025 x86_64 x86_64 x86_64 GNU/Linux, osx: 0, env: 0, pkg: 
[2025-07-30 16:00:19.297][INFO][369062][703e709l] configure detail: --prefix=/usr/local/srs --config=conf/srs.conf --osx=off --hls=on --hds=off --dvr=on --ssl=on --https=on --ssl-1-0=off --ssl-local=off --sys-ssl=off --transcode=on --ingest=on --stat=on --http-callback=on --http-server=on --stream-converter=on --http-api=on --utest=off --srt=off --sys-srt=off --rtc=on --h265=on --gb28181=off --simulator=off --cxx11=on --cxx14=off --backtrace=on --ffmpeg-fit=on --sys-ffmpeg=off --ffmpeg-opus=off --nasm=on --srtp-nasm=on --sys-srtp=off --clean=on --gperf=off --gmc=off --gmd=off --gmp=off --gcp=off --gprof=off --static=off --shared-st=off --shared-srt=reserved --shared-ffmpeg=reserved --shared-srtp=reserved --log-verbose=off --log-info=off --log-trace=on --log-level_v2=on --gcov=off --apm=off --debug=off --debug-stats=off --cross-build=off --sanitizer=on --sanitizer-static=off --sanitizer-log=off --cygwin64=off --single-thread=off --generic-linux=off --build-cache=on --cc=gcc --cxx=g++ --ar=ar --ld=ld --randlib=randlib
[2025-07-30 16:00:19.297][INFO][369062][703e709l] srs checking config...
[2025-07-30 16:00:19.298][ERROR][369062][703e709l][22] Failed, code=1023(ConfigInvalid)(Configuration is invalid) : check config : check normal : illegal rtc_server.stun
thread [369062][703e709l]: do_main() [./src/main/srs_main_server.cpp:225][errno=22]
thread [369062][703e709l]: check_config() [./src/app/srs_app_config.cpp:2287][errno=22]
thread [369062][703e709l]: check_normal_config() [./src/app/srs_app_config.cpp:2416][errno=22](Invalid argument)
