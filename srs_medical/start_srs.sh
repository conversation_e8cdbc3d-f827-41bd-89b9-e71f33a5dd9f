#!/bin/bash

# SRS医疗录制系统启动脚本

# 设置工作目录
cd /data/MedicalRecord/srs/trunk

# 设置环境变量
source /data/MedicalRecord/srs_medical/setup_env.sh

# 检测网络环境
detect_network_env

# 启动SRS服务器
echo "启动SRS医疗录制服务器..."
echo "配置文件: /data/MedicalRecord/srs_medical/medical_webrtc_recording.conf"
echo "CANDIDATE: $SRS_CANDIDATE"

# 导出环境变量供SRS使用
export CANDIDATE=$SRS_CANDIDATE

# 启动SRS
./objs/srs -c ../srs_medical/medical_webrtc_recording.conf

