#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SRS医疗录制系统管理器
用于监控流会话、管理录制文件和处理会话生命周期
"""

import os
import sys
import json
import time
import logging
import sqlite3
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import requests
from flask import Flask, request, jsonify

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/data/MedicalRecord/srs_medical/logs/recording_manager.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class RecordingManager:
    """录制管理器"""
    
    def __init__(self, config_file: str = None):
        self.config = self._load_config(config_file)
        self.db_path = self.config['database']['path']
        self.recordings_dir = Path(self.config['recordings']['base_dir'])
        self.active_sessions = {}
        self.init_database()
        
    def _load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        default_config = {
            "database": {
                "path": "/data/MedicalRecord/srs_medical/recordings.db"
            },
            "recordings": {
                "base_dir": "/data/MedicalRecord/srs_medical/recordings",
                "max_duration": 7200,  # 2小时最大录制时长
                "cleanup_days": 30     # 30天后清理录制文件
            },
            "srs": {
                "api_url": "http://localhost:1985/api/v1",
                "rtmp_port": 1935,
                "http_port": 8080
            },
            "webhook": {
                "host": "0.0.0.0",
                "port": 8888
            }
        }
        
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.warning(f"加载配置文件失败，使用默认配置: {e}")
        
        return default_config
    
    def init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建录制会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS recording_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE NOT NULL,
                    app TEXT NOT NULL,
                    stream TEXT NOT NULL,
                    client_ip TEXT,
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP,
                    duration INTEGER DEFAULT 0,
                    file_path TEXT,
                    file_size INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建录制文件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS recording_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_size INTEGER DEFAULT 0,
                    duration INTEGER DEFAULT 0,
                    format TEXT DEFAULT 'mp4',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES recording_sessions (session_id)
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def start_session(self, app: str, stream: str, client_ip: str = None) -> str:
        """开始录制会话"""
        session_id = f"{app}_{stream}_{int(time.time())}"
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO recording_sessions 
                (session_id, app, stream, client_ip, status)
                VALUES (?, ?, ?, ?, 'active')
            ''', (session_id, app, stream, client_ip))
            
            conn.commit()
            conn.close()
            
            # 添加到活跃会话
            self.active_sessions[session_id] = {
                'app': app,
                'stream': stream,
                'client_ip': client_ip,
                'start_time': datetime.now(),
                'files': []
            }
            
            logger.info(f"录制会话开始: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"开始录制会话失败: {e}")
            raise
    
    def end_session(self, session_id: str):
        """结束录制会话"""
        if session_id not in self.active_sessions:
            logger.warning(f"会话不存在: {session_id}")
            return
        
        try:
            session = self.active_sessions[session_id]
            end_time = datetime.now()
            duration = int((end_time - session['start_time']).total_seconds())
            
            # 更新数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE recording_sessions 
                SET end_time = ?, duration = ?, status = 'completed', updated_at = CURRENT_TIMESTAMP
                WHERE session_id = ?
            ''', (end_time, duration, session_id))
            
            conn.commit()
            conn.close()
            
            # 从活跃会话中移除
            del self.active_sessions[session_id]
            
            logger.info(f"录制会话结束: {session_id}, 持续时间: {duration}秒")
            
        except Exception as e:
            logger.error(f"结束录制会话失败: {e}")
    
    def add_recording_file(self, session_id: str, file_path: str):
        """添加录制文件记录"""
        if not os.path.exists(file_path):
            logger.warning(f"录制文件不存在: {file_path}")
            return
        
        try:
            file_size = os.path.getsize(file_path)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO recording_files 
                (session_id, file_path, file_size)
                VALUES (?, ?, ?)
            ''', (session_id, file_path, file_size))
            
            # 更新会话文件路径
            cursor.execute('''
                UPDATE recording_sessions 
                SET file_path = ?, file_size = ?, updated_at = CURRENT_TIMESTAMP
                WHERE session_id = ?
            ''', (file_path, file_size, session_id))
            
            conn.commit()
            conn.close()
            
            # 更新活跃会话
            if session_id in self.active_sessions:
                self.active_sessions[session_id]['files'].append(file_path)
            
            logger.info(f"录制文件已记录: {file_path}, 大小: {file_size} bytes")
            
        except Exception as e:
            logger.error(f"添加录制文件记录失败: {e}")
    
    def get_session_info(self, session_id: str) -> Optional[Dict]:
        """获取会话信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM recording_sessions WHERE session_id = ?
            ''', (session_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, row))
            
            return None
            
        except Exception as e:
            logger.error(f"获取会话信息失败: {e}")
            return None
    
    def list_active_sessions(self) -> List[Dict]:
        """列出活跃会话"""
        return list(self.active_sessions.values())
    
    def cleanup_old_recordings(self):
        """清理过期录制文件"""
        try:
            cleanup_date = datetime.now() - timedelta(days=self.config['recordings']['cleanup_days'])
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查找过期文件
            cursor.execute('''
                SELECT file_path FROM recording_sessions 
                WHERE created_at < ? AND status = 'completed'
            ''', (cleanup_date,))
            
            old_files = cursor.fetchall()
            
            for (file_path,) in old_files:
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        logger.info(f"已删除过期录制文件: {file_path}")
                    except Exception as e:
                        logger.error(f"删除文件失败: {file_path}, {e}")
            
            # 删除数据库记录
            cursor.execute('''
                DELETE FROM recording_sessions 
                WHERE created_at < ? AND status = 'completed'
            ''', (cleanup_date,))
            
            cursor.execute('''
                DELETE FROM recording_files 
                WHERE session_id NOT IN (SELECT session_id FROM recording_sessions)
            ''', )
            
            conn.commit()
            conn.close()
            
            logger.info(f"清理完成，删除了 {len(old_files)} 个过期录制文件")
            
        except Exception as e:
            logger.error(f"清理过期录制文件失败: {e}")

# Flask Webhook服务器
app = Flask(__name__)
recording_manager = RecordingManager()

@app.route('/api/v1/streams/on_publish', methods=['POST'])
def on_publish():
    """处理推流开始事件"""
    try:
        data = request.get_json()
        app_name = data.get('app', 'live')
        stream = data.get('stream', '')
        client_ip = data.get('ip', '')
        
        logger.info(f"推流开始: app={app_name}, stream={stream}, ip={client_ip}")
        
        # 开始录制会话
        session_id = recording_manager.start_session(app_name, stream, client_ip)
        
        return jsonify({'code': 0, 'session_id': session_id})
        
    except Exception as e:
        logger.error(f"处理推流开始事件失败: {e}")
        return jsonify({'code': -1, 'msg': str(e)})

@app.route('/api/v1/streams/on_unpublish', methods=['POST'])
def on_unpublish():
    """处理推流结束事件"""
    try:
        data = request.get_json()
        app_name = data.get('app', 'live')
        stream = data.get('stream', '')
        
        logger.info(f"推流结束: app={app_name}, stream={stream}")
        
        # 查找对应的会话并结束
        for session_id, session in recording_manager.active_sessions.items():
            if session['app'] == app_name and session['stream'] == stream:
                recording_manager.end_session(session_id)
                break
        
        return jsonify({'code': 0})
        
    except Exception as e:
        logger.error(f"处理推流结束事件失败: {e}")
        return jsonify({'code': -1, 'msg': str(e)})

@app.route('/api/v1/streams/on_dvr', methods=['POST'])
def on_dvr():
    """处理DVR录制事件"""
    try:
        data = request.get_json()
        app_name = data.get('app', 'live')
        stream = data.get('stream', '')
        file_path = data.get('file', '')
        
        logger.info(f"DVR录制: app={app_name}, stream={stream}, file={file_path}")
        
        # 查找对应的会话并添加文件记录
        for session_id, session in recording_manager.active_sessions.items():
            if session['app'] == app_name and session['stream'] == stream:
                recording_manager.add_recording_file(session_id, file_path)
                break
        
        return jsonify({'code': 0})
        
    except Exception as e:
        logger.error(f"处理DVR录制事件失败: {e}")
        return jsonify({'code': -1, 'msg': str(e)})

@app.route('/api/v1/sessions', methods=['GET'])
def list_sessions():
    """列出活跃会话"""
    try:
        sessions = recording_manager.list_active_sessions()
        return jsonify({'code': 0, 'data': sessions})
    except Exception as e:
        logger.error(f"列出会话失败: {e}")
        return jsonify({'code': -1, 'msg': str(e)})

@app.route('/api/v1/sessions/<session_id>', methods=['GET'])
def get_session(session_id):
    """获取会话详情"""
    try:
        session = recording_manager.get_session_info(session_id)
        if session:
            return jsonify({'code': 0, 'data': session})
        else:
            return jsonify({'code': -1, 'msg': '会话不存在'})
    except Exception as e:
        logger.error(f"获取会话详情失败: {e}")
        return jsonify({'code': -1, 'msg': str(e)})

def cleanup_worker():
    """清理工作线程"""
    while True:
        try:
            recording_manager.cleanup_old_recordings()
            time.sleep(3600)  # 每小时执行一次清理
        except Exception as e:
            logger.error(f"清理工作线程异常: {e}")
            time.sleep(60)

if __name__ == '__main__':
    # 启动清理工作线程
    cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
    cleanup_thread.start()
    
    # 启动Flask服务器
    config = recording_manager.config['webhook']
    logger.info(f"启动录制管理服务器: {config['host']}:{config['port']}")
    app.run(host=config['host'], port=config['port'], debug=False)
