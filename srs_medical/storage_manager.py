#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SRS医疗录制系统存储管理器
负责管理录制文件的存储结构、命名规则和文件组织
"""

import os
import shutil
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import json
import logging

logger = logging.getLogger(__name__)

class StorageManager:
    """存储管理器"""
    
    def __init__(self, base_dir: str = "/data/MedicalRecord/srs_medical/recordings"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 存储配置
        self.config = {
            "naming": {
                "timestamp_format": "%Y%m%d_%H%M%S",
                "session_id_length": 8,
                "include_hash": True
            },
            "structure": {
                "by_date": True,
                "by_app": True,
                "by_stream": False,
                "max_depth": 4
            },
            "cleanup": {
                "auto_cleanup": True,
                "retention_days": 30,
                "archive_old_files": True,
                "archive_threshold_days": 7
            },
            "quality": {
                "backup_enabled": False,
                "checksum_enabled": True,
                "compression_enabled": False
            }
        }
    
    def generate_file_path(self, app: str, stream: str, session_id: str = None, 
                          timestamp: datetime = None, format_type: str = "mp4") -> Path:
        """生成录制文件路径"""
        if timestamp is None:
            timestamp = datetime.now()
        
        # 生成会话ID（如果未提供）
        if session_id is None:
            session_id = self._generate_session_id(app, stream, timestamp)
        
        # 构建目录结构
        year = timestamp.strftime("%Y")
        month = timestamp.strftime("%m")
        day = timestamp.strftime("%d")
        
        if self.config["structure"]["by_app"]:
            if self.config["structure"]["by_date"]:
                # 结构: app/年/月/日/
                dir_path = self.base_dir / app / year / month / day
            else:
                # 结构: app/
                dir_path = self.base_dir / app
        else:
            if self.config["structure"]["by_date"]:
                # 结构: 年/月/日/
                dir_path = self.base_dir / year / month / day
            else:
                # 结构: 根目录
                dir_path = self.base_dir
        
        # 生成文件名
        timestamp_str = timestamp.strftime(self.config["naming"]["timestamp_format"])
        
        if self.config["naming"]["include_hash"]:
            # 包含内容哈希的文件名
            content_hash = self._generate_content_hash(app, stream, session_id, timestamp)
            filename = f"{stream}_{timestamp_str}_{content_hash[:8]}.{format_type}"
        else:
            # 简单的文件名
            filename = f"{stream}_{timestamp_str}.{format_type}"
        
        return dir_path / filename
    
    def _generate_session_id(self, app: str, stream: str, timestamp: datetime) -> str:
        """生成会话ID"""
        content = f"{app}_{stream}_{timestamp.isoformat()}"
        hash_obj = hashlib.md5(content.encode())
        return hash_obj.hexdigest()[:self.config["naming"]["session_id_length"]]
    
    def _generate_content_hash(self, app: str, stream: str, session_id: str, timestamp: datetime) -> str:
        """生成内容哈希"""
        content = f"{app}_{stream}_{session_id}_{timestamp.isoformat()}"
        hash_obj = hashlib.sha256(content.encode())
        return hash_obj.hexdigest()
    
    def create_directory_structure(self, file_path: Path) -> bool:
        """创建目录结构"""
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 设置目录权限
            os.chmod(file_path.parent, 0o755)
            
            logger.info(f"创建目录结构: {file_path.parent}")
            return True
            
        except Exception as e:
            logger.error(f"创建目录结构失败: {e}")
            return False
    
    def get_file_info(self, file_path: Path) -> Dict:
        """获取文件信息"""
        if not file_path.exists():
            return {}
        
        stat = file_path.stat()
        
        info = {
            "path": str(file_path),
            "name": file_path.name,
            "size": stat.st_size,
            "created": datetime.fromtimestamp(stat.st_ctime),
            "modified": datetime.fromtimestamp(stat.st_mtime),
            "extension": file_path.suffix.lower(),
            "directory": str(file_path.parent)
        }
        
        # 计算文件哈希（如果启用）
        if self.config["quality"]["checksum_enabled"]:
            info["checksum"] = self._calculate_file_hash(file_path)
        
        return info
    
    def _calculate_file_hash(self, file_path: Path, algorithm: str = "md5") -> str:
        """计算文件哈希值"""
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {e}")
            return ""
    
    def list_recordings(self, app: str = None, date_range: Tuple[datetime, datetime] = None,
                       limit: int = None) -> List[Dict]:
        """列出录制文件"""
        recordings = []
        
        # 确定搜索路径
        if app:
            search_paths = [self.base_dir / app]
        else:
            search_paths = [self.base_dir]
        
        for search_path in search_paths:
            if not search_path.exists():
                continue
            
            # 递归搜索录制文件
            for file_path in search_path.rglob("*.mp4"):
                file_info = self.get_file_info(file_path)
                
                # 日期范围过滤
                if date_range:
                    start_date, end_date = date_range
                    if not (start_date <= file_info["created"] <= end_date):
                        continue
                
                recordings.append(file_info)
        
        # 按创建时间排序
        recordings.sort(key=lambda x: x["created"], reverse=True)
        
        # 限制数量
        if limit:
            recordings = recordings[:limit]
        
        return recordings
    
    def archive_old_files(self, threshold_days: int = None) -> int:
        """归档旧文件"""
        if threshold_days is None:
            threshold_days = self.config["cleanup"]["archive_threshold_days"]
        
        threshold_date = datetime.now() - timedelta(days=threshold_days)
        archive_dir = self.base_dir / "archive"
        archive_dir.mkdir(exist_ok=True)
        
        archived_count = 0
        
        # 查找需要归档的文件
        for file_path in self.base_dir.rglob("*.mp4"):
            if file_path.parent == archive_dir:
                continue  # 跳过已归档的文件
            
            stat = file_path.stat()
            created_date = datetime.fromtimestamp(stat.st_ctime)
            
            if created_date < threshold_date:
                try:
                    # 保持相对路径结构
                    relative_path = file_path.relative_to(self.base_dir)
                    archive_path = archive_dir / relative_path
                    archive_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 移动文件到归档目录
                    shutil.move(str(file_path), str(archive_path))
                    archived_count += 1
                    
                    logger.info(f"文件已归档: {file_path} -> {archive_path}")
                    
                except Exception as e:
                    logger.error(f"归档文件失败: {file_path} - {e}")
        
        return archived_count
    
    def cleanup_empty_directories(self) -> int:
        """清理空目录"""
        removed_count = 0
        
        # 从最深层开始清理
        for dir_path in sorted(self.base_dir.rglob("*"), key=lambda p: len(p.parts), reverse=True):
            if dir_path.is_dir() and dir_path != self.base_dir:
                try:
                    # 检查目录是否为空
                    if not any(dir_path.iterdir()):
                        dir_path.rmdir()
                        removed_count += 1
                        logger.info(f"已删除空目录: {dir_path}")
                except Exception as e:
                    logger.debug(f"删除目录失败: {dir_path} - {e}")
        
        return removed_count
    
    def get_storage_stats(self) -> Dict:
        """获取存储统计信息"""
        stats = {
            "total_files": 0,
            "total_size": 0,
            "by_app": {},
            "by_date": {},
            "by_format": {},
            "oldest_file": None,
            "newest_file": None
        }
        
        for file_path in self.base_dir.rglob("*"):
            if file_path.is_file():
                file_info = self.get_file_info(file_path)
                
                stats["total_files"] += 1
                stats["total_size"] += file_info["size"]
                
                # 按应用统计
                try:
                    relative_path = file_path.relative_to(self.base_dir)
                    app = relative_path.parts[0] if relative_path.parts else "unknown"
                    if app not in stats["by_app"]:
                        stats["by_app"][app] = {"files": 0, "size": 0}
                    stats["by_app"][app]["files"] += 1
                    stats["by_app"][app]["size"] += file_info["size"]
                except:
                    pass
                
                # 按日期统计
                date_key = file_info["created"].strftime("%Y-%m-%d")
                if date_key not in stats["by_date"]:
                    stats["by_date"][date_key] = {"files": 0, "size": 0}
                stats["by_date"][date_key]["files"] += 1
                stats["by_date"][date_key]["size"] += file_info["size"]
                
                # 按格式统计
                format_key = file_info["extension"]
                if format_key not in stats["by_format"]:
                    stats["by_format"][format_key] = {"files": 0, "size": 0}
                stats["by_format"][format_key]["files"] += 1
                stats["by_format"][format_key]["size"] += file_info["size"]
                
                # 最新和最旧文件
                if stats["oldest_file"] is None or file_info["created"] < stats["oldest_file"]["created"]:
                    stats["oldest_file"] = file_info
                if stats["newest_file"] is None or file_info["created"] > stats["newest_file"]["created"]:
                    stats["newest_file"] = file_info
        
        return stats
    
    def validate_file_integrity(self, file_path: Path) -> bool:
        """验证文件完整性"""
        if not file_path.exists():
            return False
        
        try:
            # 检查文件大小
            if file_path.stat().st_size == 0:
                logger.warning(f"文件为空: {file_path}")
                return False
            
            # 检查文件格式（简单检查）
            if file_path.suffix.lower() == ".mp4":
                # 检查MP4文件头
                with open(file_path, 'rb') as f:
                    header = f.read(8)
                    if len(header) < 8:
                        return False
                    # MP4文件应该包含ftyp box
                    if b'ftyp' not in header:
                        logger.warning(f"MP4文件格式可能损坏: {file_path}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证文件完整性失败: {file_path} - {e}")
            return False

def main():
    """命令行工具"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SRS医疗录制存储管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 列出录制文件
    list_parser = subparsers.add_parser('list', help='列出录制文件')
    list_parser.add_argument('--app', help='应用名称过滤')
    list_parser.add_argument('--limit', type=int, default=20, help='显示数量限制')
    
    # 存储统计
    stats_parser = subparsers.add_parser('stats', help='显示存储统计')
    
    # 归档旧文件
    archive_parser = subparsers.add_parser('archive', help='归档旧文件')
    archive_parser.add_argument('--days', type=int, default=7, help='归档多少天前的文件')
    
    # 清理空目录
    cleanup_parser = subparsers.add_parser('cleanup', help='清理空目录')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    storage = StorageManager()
    
    if args.command == 'list':
        recordings = storage.list_recordings(app=args.app, limit=args.limit)
        
        print(f"{'文件名':<40} {'大小':<15} {'创建时间':<20} {'路径':<50}")
        print("-" * 130)
        
        for recording in recordings:
            size_mb = recording['size'] / (1024 * 1024)
            created = recording['created'].strftime('%Y-%m-%d %H:%M:%S')
            print(f"{recording['name']:<40} {size_mb:>10.1f} MB {created:<20} {recording['directory']:<50}")
    
    elif args.command == 'stats':
        stats = storage.get_storage_stats()
        
        print("=== 存储统计信息 ===")
        print(f"总文件数: {stats['total_files']}")
        print(f"总大小: {stats['total_size'] / (1024**3):.2f} GB")
        
        print("\n按应用统计:")
        for app, data in stats['by_app'].items():
            print(f"  {app}: {data['files']} 文件, {data['size'] / (1024**2):.1f} MB")
        
        print("\n按格式统计:")
        for fmt, data in stats['by_format'].items():
            print(f"  {fmt}: {data['files']} 文件, {data['size'] / (1024**2):.1f} MB")
    
    elif args.command == 'archive':
        count = storage.archive_old_files(args.days)
        print(f"已归档 {count} 个文件")
    
    elif args.command == 'cleanup':
        count = storage.cleanup_empty_directories()
        print(f"已清理 {count} 个空目录")

if __name__ == '__main__':
    main()
