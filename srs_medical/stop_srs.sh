#!/bin/bash

# SRS医疗录制系统停止脚本

echo "停止SRS医疗录制服务器..."

# 查找SRS进程
SRS_PID=$(ps aux | grep '[s]rs.*medical_webrtc_recording.conf' | awk '{print $2}')

if [ -n "$SRS_PID" ]; then
    echo "找到SRS进程 PID: $SRS_PID"
    kill -TERM $SRS_PID
    
    # 等待进程结束
    sleep 3
    
    # 检查进程是否还在运行
    if kill -0 $SRS_PID 2>/dev/null; then
        echo "强制终止SRS进程"
        kill -KILL $SRS_PID
    fi
    
    echo "SRS服务器已停止"
else
    echo "未找到运行中的SRS进程"
fi

# 清理PID文件
if [ -f "/data/MedicalRecord/srs_medical/srs.pid" ]; then
    rm -f "/data/MedicalRecord/srs_medical/srs.pid"
fi

