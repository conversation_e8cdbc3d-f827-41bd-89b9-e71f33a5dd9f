#!/bin/bash

# SRS医疗录制系统环境配置脚本
# 用于设置WebRTC网络参数和系统环境

echo "=== SRS医疗录制系统环境配置 ==="

# 获取本机IP地址
get_local_ip() {
    # 尝试多种方法获取本机IP
    local ip=""
    
    # 方法1: 通过默认路由获取
    ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
    
    # 方法2: 通过网络接口获取
    if [ -z "$ip" ]; then
        ip=$(hostname -I | awk '{print $1}')
    fi
    
    # 方法3: 通过ifconfig获取
    if [ -z "$ip" ]; then
        ip=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
    fi
    
    # 默认使用localhost
    if [ -z "$ip" ]; then
        ip="127.0.0.1"
    fi
    
    echo "$ip"
}

# 检测网络环境
detect_network_env() {
    echo "检测网络环境..."
    
    local_ip=$(get_local_ip)
    echo "检测到本机IP: $local_ip"
    
    # 检查是否为内网IP
    if [[ $local_ip =~ ^192\.168\. ]] || [[ $local_ip =~ ^10\. ]] || [[ $local_ip =~ ^172\.(1[6-9]|2[0-9]|3[0-1])\. ]]; then
        echo "检测到内网环境"
        export SRS_CANDIDATE="$local_ip"
    else
        echo "检测到公网环境"
        export SRS_CANDIDATE="$local_ip"
    fi
    
    echo "设置CANDIDATE为: $SRS_CANDIDATE"
}

# 创建必要的目录
create_directories() {
    echo "创建必要的目录结构..."
    
    local base_dir="/data/MedicalRecord/srs_medical"
    
    # 创建目录
    mkdir -p "$base_dir/logs"
    mkdir -p "$base_dir/recordings/medical"
    mkdir -p "$base_dir/hls"
    mkdir -p "$base_dir/www"
    mkdir -p "$base_dir/scripts"
    mkdir -p "$base_dir/config"
    
    echo "目录创建完成"
}

# 设置系统参数优化
optimize_system() {
    echo "优化系统参数..."
    
    # 检查是否有root权限
    if [ "$EUID" -eq 0 ]; then
        # 增加文件描述符限制
        echo "* soft nofile 65536" >> /etc/security/limits.conf
        echo "* hard nofile 65536" >> /etc/security/limits.conf
        
        # 优化网络参数
        echo "net.core.rmem_max = 134217728" >> /etc/sysctl.conf
        echo "net.core.wmem_max = 134217728" >> /etc/sysctl.conf
        echo "net.ipv4.tcp_rmem = 4096 65536 134217728" >> /etc/sysctl.conf
        echo "net.ipv4.tcp_wmem = 4096 65536 134217728" >> /etc/sysctl.conf
        echo "net.core.netdev_max_backlog = 5000" >> /etc/sysctl.conf
        
        # 应用系统参数
        sysctl -p
        
        echo "系统参数优化完成"
    else
        echo "警告: 需要root权限进行系统优化，跳过此步骤"
    fi
}

# 检查端口占用
check_ports() {
    echo "检查端口占用情况..."
    
    local ports=(1935 **************)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if netstat -tuln | grep -q ":$port "; then
            occupied_ports+=($port)
            echo "警告: 端口 $port 已被占用"
        else
            echo "端口 $port 可用"
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        echo "发现端口占用，请检查以下端口: ${occupied_ports[*]}"
        echo "可以使用以下命令查看占用进程:"
        for port in "${occupied_ports[@]}"; do
            echo "  sudo lsof -i :$port"
        done
        return 1
    fi
    
    return 0
}

# 生成启动脚本
generate_start_script() {
    echo "生成启动脚本..."
    
    cat > /data/MedicalRecord/srs_medical/start_srs.sh << 'EOF'
#!/bin/bash

# SRS医疗录制系统启动脚本

# 设置工作目录
cd /data/MedicalRecord/srs/trunk

# 设置环境变量
source /data/MedicalRecord/srs_medical/setup_env.sh

# 检测网络环境
detect_network_env

# 启动SRS服务器
echo "启动SRS医疗录制服务器..."
echo "配置文件: /data/MedicalRecord/srs_medical/medical_webrtc_recording.conf"
echo "CANDIDATE: $SRS_CANDIDATE"

# 导出环境变量供SRS使用
export CANDIDATE=$SRS_CANDIDATE

# 启动SRS
./objs/srs -c ../srs_medical/medical_webrtc_recording.conf

EOF

    chmod +x /data/MedicalRecord/srs_medical/start_srs.sh
    echo "启动脚本已生成: /data/MedicalRecord/srs_medical/start_srs.sh"
}

# 生成停止脚本
generate_stop_script() {
    echo "生成停止脚本..."
    
    cat > /data/MedicalRecord/srs_medical/stop_srs.sh << 'EOF'
#!/bin/bash

# SRS医疗录制系统停止脚本

echo "停止SRS医疗录制服务器..."

# 查找SRS进程
SRS_PID=$(ps aux | grep '[s]rs.*medical_webrtc_recording.conf' | awk '{print $2}')

if [ -n "$SRS_PID" ]; then
    echo "找到SRS进程 PID: $SRS_PID"
    kill -TERM $SRS_PID
    
    # 等待进程结束
    sleep 3
    
    # 检查进程是否还在运行
    if kill -0 $SRS_PID 2>/dev/null; then
        echo "强制终止SRS进程"
        kill -KILL $SRS_PID
    fi
    
    echo "SRS服务器已停止"
else
    echo "未找到运行中的SRS进程"
fi

# 清理PID文件
if [ -f "/data/MedicalRecord/srs_medical/srs.pid" ]; then
    rm -f "/data/MedicalRecord/srs_medical/srs.pid"
fi

EOF

    chmod +x /data/MedicalRecord/srs_medical/stop_srs.sh
    echo "停止脚本已生成: /data/MedicalRecord/srs_medical/stop_srs.sh"
}

# 主函数
main() {
    echo "开始配置SRS医疗录制系统环境..."
    
    # 检测网络环境
    detect_network_env
    
    # 创建目录
    create_directories
    
    # 检查端口
    if ! check_ports; then
        echo "端口检查失败，请解决端口占用问题后重新运行"
        exit 1
    fi
    
    # 系统优化
    optimize_system
    
    # 生成脚本
    generate_start_script
    generate_stop_script
    
    echo ""
    echo "=== 环境配置完成 ==="
    echo "CANDIDATE IP: $SRS_CANDIDATE"
    echo "启动命令: /data/MedicalRecord/srs_medical/start_srs.sh"
    echo "停止命令: /data/MedicalRecord/srs_medical/stop_srs.sh"
    echo "Web界面: http://localhost:8080"
    echo "API接口: http://localhost:1985/api/v1/"
    echo ""
}

# 如果直接执行此脚本，运行主函数
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
