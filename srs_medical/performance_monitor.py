#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SRS医疗录制系统性能监控器
监控系统性能、资源使用情况和录制质量
"""

import os
import sys
import time
import json
import psutil
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
import threading
import sqlite3

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, config_file: str = None):
        self.config = self._load_config(config_file)
        self.db_path = self.config['database']['path']
        self.srs_api = self.config['srs']['api_url']
        self.monitoring = True
        self.init_database()
    
    def _load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        default_config = {
            "database": {
                "path": "/data/MedicalRecord/srs_medical/performance.db"
            },
            "srs": {
                "api_url": "http://localhost:1985/api/v1",
                "process_name": "srs"
            },
            "monitoring": {
                "interval": 30,
                "cpu_threshold": 80,
                "memory_threshold": 80,
                "disk_threshold": 85,
                "network_threshold": 100  # Mbps
            },
            "alerts": {
                "enabled": True,
                "webhook_url": None,
                "email_enabled": False
            }
        }
        
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.warning(f"加载配置文件失败，使用默认配置: {e}")
        
        return default_config
    
    def init_database(self):
        """初始化性能数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 系统性能表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    cpu_percent REAL,
                    memory_percent REAL,
                    disk_percent REAL,
                    network_sent_mbps REAL,
                    network_recv_mbps REAL,
                    active_connections INTEGER,
                    srs_cpu_percent REAL,
                    srs_memory_mb REAL
                )
            ''')
            
            # 流性能表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stream_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    app TEXT,
                    stream TEXT,
                    bitrate_kbps INTEGER,
                    fps INTEGER,
                    resolution TEXT,
                    packet_loss_rate REAL,
                    latency_ms INTEGER,
                    client_count INTEGER
                )
            ''')
            
            # 告警记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    alert_type TEXT,
                    severity TEXT,
                    message TEXT,
                    resolved BOOLEAN DEFAULT FALSE,
                    resolved_at TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("性能监控数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def get_system_performance(self) -> Dict:
        """获取系统性能指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/data/MedicalRecord/srs_medical')
            disk_percent = disk.percent
            
            # 网络使用率
            network_before = psutil.net_io_counters()
            time.sleep(1)
            network_after = psutil.net_io_counters()
            
            network_sent_mbps = (network_after.bytes_sent - network_before.bytes_sent) * 8 / (1024 * 1024)
            network_recv_mbps = (network_after.bytes_recv - network_before.bytes_recv) * 8 / (1024 * 1024)
            
            # 活跃连接数
            active_connections = len(psutil.net_connections())
            
            # SRS进程性能
            srs_cpu_percent = 0
            srs_memory_mb = 0
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info']):
                try:
                    if self.config['srs']['process_name'] in proc.info['name']:
                        srs_cpu_percent = proc.info['cpu_percent'] or 0
                        srs_memory_mb = (proc.info['memory_info'].rss / 1024 / 1024) if proc.info['memory_info'] else 0
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return {
                'timestamp': datetime.now(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent,
                'network_sent_mbps': network_sent_mbps,
                'network_recv_mbps': network_recv_mbps,
                'active_connections': active_connections,
                'srs_cpu_percent': srs_cpu_percent,
                'srs_memory_mb': srs_memory_mb
            }
            
        except Exception as e:
            logger.error(f"获取系统性能指标失败: {e}")
            return {}
    
    def get_srs_streams(self) -> List[Dict]:
        """获取SRS流信息"""
        try:
            response = requests.get(f"{self.srs_api}/streams", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return data.get('streams', [])
            else:
                logger.warning(f"SRS API请求失败: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"获取SRS流信息失败: {e}")
            return []
    
    def get_stream_performance(self) -> List[Dict]:
        """获取流性能指标"""
        streams = self.get_srs_streams()
        performance_data = []
        
        for stream in streams:
            try:
                app = stream.get('app', '')
                stream_name = stream.get('name', '')
                
                # 获取流统计信息
                stream_url = f"{self.srs_api}/streams/{app}/{stream_name}"
                response = requests.get(stream_url, timeout=5)
                
                if response.status_code == 200:
                    stream_data = response.json()
                    stream_info = stream_data.get('stream', {})
                    
                    performance_data.append({
                        'timestamp': datetime.now(),
                        'app': app,
                        'stream': stream_name,
                        'bitrate_kbps': stream_info.get('video', {}).get('bitrate', 0) // 1000,
                        'fps': stream_info.get('video', {}).get('fps', 0),
                        'resolution': f"{stream_info.get('video', {}).get('width', 0)}x{stream_info.get('video', {}).get('height', 0)}",
                        'packet_loss_rate': stream_info.get('network', {}).get('loss_rate', 0),
                        'latency_ms': stream_info.get('network', {}).get('latency', 0),
                        'client_count': len(stream.get('clients', []))
                    })
                    
            except Exception as e:
                logger.error(f"获取流 {app}/{stream_name} 性能数据失败: {e}")
        
        return performance_data
    
    def save_performance_data(self, system_perf: Dict, stream_perfs: List[Dict]):
        """保存性能数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 保存系统性能数据
            if system_perf:
                cursor.execute('''
                    INSERT INTO system_performance 
                    (cpu_percent, memory_percent, disk_percent, network_sent_mbps, 
                     network_recv_mbps, active_connections, srs_cpu_percent, srs_memory_mb)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    system_perf['cpu_percent'],
                    system_perf['memory_percent'],
                    system_perf['disk_percent'],
                    system_perf['network_sent_mbps'],
                    system_perf['network_recv_mbps'],
                    system_perf['active_connections'],
                    system_perf['srs_cpu_percent'],
                    system_perf['srs_memory_mb']
                ))
            
            # 保存流性能数据
            for stream_perf in stream_perfs:
                cursor.execute('''
                    INSERT INTO stream_performance 
                    (app, stream, bitrate_kbps, fps, resolution, packet_loss_rate, 
                     latency_ms, client_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    stream_perf['app'],
                    stream_perf['stream'],
                    stream_perf['bitrate_kbps'],
                    stream_perf['fps'],
                    stream_perf['resolution'],
                    stream_perf['packet_loss_rate'],
                    stream_perf['latency_ms'],
                    stream_perf['client_count']
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"保存性能数据失败: {e}")
    
    def check_alerts(self, system_perf: Dict, stream_perfs: List[Dict]):
        """检查告警条件"""
        alerts = []
        
        # 系统性能告警
        if system_perf.get('cpu_percent', 0) > self.config['monitoring']['cpu_threshold']:
            alerts.append({
                'type': 'system_cpu',
                'severity': 'warning',
                'message': f"CPU使用率过高: {system_perf['cpu_percent']:.1f}%"
            })
        
        if system_perf.get('memory_percent', 0) > self.config['monitoring']['memory_threshold']:
            alerts.append({
                'type': 'system_memory',
                'severity': 'warning',
                'message': f"内存使用率过高: {system_perf['memory_percent']:.1f}%"
            })
        
        if system_perf.get('disk_percent', 0) > self.config['monitoring']['disk_threshold']:
            alerts.append({
                'type': 'system_disk',
                'severity': 'critical',
                'message': f"磁盘使用率过高: {system_perf['disk_percent']:.1f}%"
            })
        
        # SRS进程告警
        if system_perf.get('srs_cpu_percent', 0) > 90:
            alerts.append({
                'type': 'srs_cpu',
                'severity': 'warning',
                'message': f"SRS CPU使用率过高: {system_perf['srs_cpu_percent']:.1f}%"
            })
        
        # 流性能告警
        for stream_perf in stream_perfs:
            if stream_perf.get('packet_loss_rate', 0) > 0.05:  # 5%丢包率
                alerts.append({
                    'type': 'stream_loss',
                    'severity': 'warning',
                    'message': f"流 {stream_perf['app']}/{stream_perf['stream']} 丢包率过高: {stream_perf['packet_loss_rate']*100:.1f}%"
                })
            
            if stream_perf.get('latency_ms', 0) > 5000:  # 5秒延迟
                alerts.append({
                    'type': 'stream_latency',
                    'severity': 'warning',
                    'message': f"流 {stream_perf['app']}/{stream_perf['stream']} 延迟过高: {stream_perf['latency_ms']}ms"
                })
        
        # 保存告警
        if alerts:
            self.save_alerts(alerts)
        
        return alerts
    
    def save_alerts(self, alerts: List[Dict]):
        """保存告警信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for alert in alerts:
                cursor.execute('''
                    INSERT INTO alerts (alert_type, severity, message)
                    VALUES (?, ?, ?)
                ''', (alert['type'], alert['severity'], alert['message']))
            
            conn.commit()
            conn.close()
            
            # 发送告警通知
            if self.config['alerts']['enabled']:
                self.send_alert_notifications(alerts)
            
        except Exception as e:
            logger.error(f"保存告警信息失败: {e}")
    
    def send_alert_notifications(self, alerts: List[Dict]):
        """发送告警通知"""
        try:
            if self.config['alerts']['webhook_url']:
                # 发送Webhook通知
                payload = {
                    'timestamp': datetime.now().isoformat(),
                    'alerts': alerts
                }
                
                response = requests.post(
                    self.config['alerts']['webhook_url'],
                    json=payload,
                    timeout=10
                )
                
                if response.status_code == 200:
                    logger.info("告警通知发送成功")
                else:
                    logger.warning(f"告警通知发送失败: {response.status_code}")
            
        except Exception as e:
            logger.error(f"发送告警通知失败: {e}")
    
    def get_performance_summary(self, hours: int = 24) -> Dict:
        """获取性能摘要"""
        try:
            start_time = datetime.now() - timedelta(hours=hours)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 系统性能摘要
            cursor.execute('''
                SELECT 
                    AVG(cpu_percent) as avg_cpu,
                    MAX(cpu_percent) as max_cpu,
                    AVG(memory_percent) as avg_memory,
                    MAX(memory_percent) as max_memory,
                    AVG(disk_percent) as avg_disk,
                    MAX(disk_percent) as max_disk,
                    AVG(srs_cpu_percent) as avg_srs_cpu,
                    MAX(srs_cpu_percent) as max_srs_cpu,
                    AVG(srs_memory_mb) as avg_srs_memory,
                    MAX(srs_memory_mb) as max_srs_memory
                FROM system_performance 
                WHERE timestamp > ?
            ''', (start_time,))
            
            system_summary = cursor.fetchone()
            
            # 流统计摘要
            cursor.execute('''
                SELECT 
                    COUNT(DISTINCT app || '/' || stream) as unique_streams,
                    AVG(bitrate_kbps) as avg_bitrate,
                    AVG(fps) as avg_fps,
                    AVG(packet_loss_rate) as avg_loss_rate,
                    AVG(latency_ms) as avg_latency,
                    SUM(client_count) as total_clients
                FROM stream_performance 
                WHERE timestamp > ?
            ''', (start_time,))
            
            stream_summary = cursor.fetchone()
            
            # 告警统计
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_alerts,
                    COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_alerts,
                    COUNT(CASE WHEN severity = 'warning' THEN 1 END) as warning_alerts
                FROM alerts 
                WHERE timestamp > ?
            ''', (start_time,))
            
            alert_summary = cursor.fetchone()
            
            conn.close()
            
            return {
                'period_hours': hours,
                'system': {
                    'avg_cpu': system_summary[0] or 0,
                    'max_cpu': system_summary[1] or 0,
                    'avg_memory': system_summary[2] or 0,
                    'max_memory': system_summary[3] or 0,
                    'avg_disk': system_summary[4] or 0,
                    'max_disk': system_summary[5] or 0,
                    'avg_srs_cpu': system_summary[6] or 0,
                    'max_srs_cpu': system_summary[7] or 0,
                    'avg_srs_memory': system_summary[8] or 0,
                    'max_srs_memory': system_summary[9] or 0
                },
                'streams': {
                    'unique_streams': stream_summary[0] or 0,
                    'avg_bitrate': stream_summary[1] or 0,
                    'avg_fps': stream_summary[2] or 0,
                    'avg_loss_rate': stream_summary[3] or 0,
                    'avg_latency': stream_summary[4] or 0,
                    'total_clients': stream_summary[5] or 0
                },
                'alerts': {
                    'total_alerts': alert_summary[0] or 0,
                    'critical_alerts': alert_summary[1] or 0,
                    'warning_alerts': alert_summary[2] or 0
                }
            }
            
        except Exception as e:
            logger.error(f"获取性能摘要失败: {e}")
            return {}
    
    def start_monitoring(self):
        """开始性能监控"""
        logger.info("开始性能监控")
        
        while self.monitoring:
            try:
                # 获取性能数据
                system_perf = self.get_system_performance()
                stream_perfs = self.get_stream_performance()
                
                # 保存数据
                self.save_performance_data(system_perf, stream_perfs)
                
                # 检查告警
                alerts = self.check_alerts(system_perf, stream_perfs)
                
                if alerts:
                    logger.warning(f"检测到 {len(alerts)} 个告警")
                
                # 等待下次监控
                time.sleep(self.config['monitoring']['interval'])
                
            except Exception as e:
                logger.error(f"性能监控异常: {e}")
                time.sleep(10)  # 异常时短暂等待
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        logger.info("性能监控已停止")

def main():
    """命令行工具"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SRS医疗录制性能监控工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 启动监控
    start_parser = subparsers.add_parser('start', help='启动性能监控')
    start_parser.add_argument('--config', help='配置文件路径')
    
    # 性能摘要
    summary_parser = subparsers.add_parser('summary', help='显示性能摘要')
    summary_parser.add_argument('--hours', type=int, default=24, help='统计时间范围（小时）')
    
    # 当前状态
    status_parser = subparsers.add_parser('status', help='显示当前状态')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    monitor = PerformanceMonitor(getattr(args, 'config', None))
    
    if args.command == 'start':
        try:
            monitor.start_monitoring()
        except KeyboardInterrupt:
            monitor.stop_monitoring()
    
    elif args.command == 'summary':
        summary = monitor.get_performance_summary(args.hours)
        
        print(f"=== 性能摘要 (最近{args.hours}小时) ===")
        print(f"系统性能:")
        print(f"  平均CPU: {summary['system']['avg_cpu']:.1f}%")
        print(f"  最大CPU: {summary['system']['max_cpu']:.1f}%")
        print(f"  平均内存: {summary['system']['avg_memory']:.1f}%")
        print(f"  最大内存: {summary['system']['max_memory']:.1f}%")
        print(f"  平均磁盘: {summary['system']['avg_disk']:.1f}%")
        
        print(f"\nSRS性能:")
        print(f"  平均CPU: {summary['system']['avg_srs_cpu']:.1f}%")
        print(f"  平均内存: {summary['system']['avg_srs_memory']:.1f}MB")
        
        print(f"\n流统计:")
        print(f"  独立流数: {summary['streams']['unique_streams']}")
        print(f"  平均码率: {summary['streams']['avg_bitrate']:.0f}kbps")
        print(f"  平均帧率: {summary['streams']['avg_fps']:.1f}fps")
        print(f"  平均丢包率: {summary['streams']['avg_loss_rate']*100:.2f}%")
        print(f"  总客户端: {summary['streams']['total_clients']}")
        
        print(f"\n告警统计:")
        print(f"  总告警数: {summary['alerts']['total_alerts']}")
        print(f"  严重告警: {summary['alerts']['critical_alerts']}")
        print(f"  警告告警: {summary['alerts']['warning_alerts']}")
    
    elif args.command == 'status':
        system_perf = monitor.get_system_performance()
        streams = monitor.get_srs_streams()
        
        print("=== 当前系统状态 ===")
        print(f"CPU使用率: {system_perf.get('cpu_percent', 0):.1f}%")
        print(f"内存使用率: {system_perf.get('memory_percent', 0):.1f}%")
        print(f"磁盘使用率: {system_perf.get('disk_percent', 0):.1f}%")
        print(f"SRS CPU: {system_perf.get('srs_cpu_percent', 0):.1f}%")
        print(f"SRS 内存: {system_perf.get('srs_memory_mb', 0):.1f}MB")
        print(f"活跃连接: {system_perf.get('active_connections', 0)}")
        print(f"当前流数: {len(streams)}")

if __name__ == '__main__':
    main()
