#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SRS医疗录制系统测试验证脚本
全面测试WebRTC流媒体录制功能
"""

import os
import sys
import time
import json
import requests
import subprocess
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
import sqlite3
import hashlib

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SRSMedicalTester:
    """SRS医疗录制系统测试器"""
    
    def __init__(self, base_dir: str = "/data/MedicalRecord"):
        self.base_dir = Path(base_dir)
        self.srs_medical_dir = self.base_dir / "srs_medical"
        
        # 测试配置
        self.config = {
            'srs_api': 'http://localhost:1985/api/v1',
            'recording_api': 'http://localhost:8888/api/v1',
            'webrtc_url': 'http://localhost:8080',
            'test_app': 'medical_test',
            'test_stream': 'test_stream_001',
            'test_duration': 30,  # 秒
            'recording_dir': str(self.srs_medical_dir / "recordings"),
            'db_path': str(self.srs_medical_dir / "recordings.db")
        }
        
        # 测试结果
        self.test_results = []
        self.start_time = None
        self.end_time = None
    
    def log_test_result(self, test_name: str, success: bool, message: str = "", details: Dict = None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'details': details or {},
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✓ PASS" if success else "✗ FAIL"
        logger.info(f"{status} {test_name}: {message}")
    
    def test_service_connectivity(self) -> bool:
        """测试服务连接性"""
        logger.info("=== 测试服务连接性 ===")
        
        # 测试SRS API
        try:
            response = requests.get(f"{self.config['srs_api']}/summaries", timeout=5)
            if response.status_code == 200:
                self.log_test_result("SRS API连接", True, "SRS API响应正常")
            else:
                self.log_test_result("SRS API连接", False, f"SRS API响应异常: {response.status_code}")
                return False
        except Exception as e:
            self.log_test_result("SRS API连接", False, f"SRS API连接失败: {e}")
            return False
        
        # 测试录制管理API
        try:
            response = requests.get(f"{self.config['recording_api']}/health", timeout=5)
            if response.status_code == 200:
                self.log_test_result("录制管理API连接", True, "录制管理API响应正常")
            else:
                self.log_test_result("录制管理API连接", False, f"录制管理API响应异常: {response.status_code}")
                return False
        except Exception as e:
            self.log_test_result("录制管理API连接", False, f"录制管理API连接失败: {e}")
            return False
        
        # 测试WebRTC页面
        try:
            response = requests.get(self.config['webrtc_url'], timeout=5)
            if response.status_code == 200:
                self.log_test_result("WebRTC页面访问", True, "WebRTC测试页面可访问")
            else:
                self.log_test_result("WebRTC页面访问", False, f"WebRTC页面响应异常: {response.status_code}")
                return False
        except Exception as e:
            self.log_test_result("WebRTC页面访问", False, f"WebRTC页面访问失败: {e}")
            return False
        
        return True
    
    def test_database_connectivity(self) -> bool:
        """测试数据库连接"""
        logger.info("=== 测试数据库连接 ===")
        
        try:
            conn = sqlite3.connect(self.config['db_path'])
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['recording_sessions', 'recording_files']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                self.log_test_result("数据库表检查", False, f"缺少表: {missing_tables}")
                conn.close()
                return False
            
            # 测试插入和查询
            test_session_id = f"test_{int(time.time())}"
            cursor.execute('''
                INSERT INTO recording_sessions 
                (session_id, app, stream, client_ip, status)
                VALUES (?, ?, ?, ?, ?)
            ''', (test_session_id, 'test_app', 'test_stream', '127.0.0.1', 'testing'))
            
            cursor.execute('SELECT * FROM recording_sessions WHERE session_id = ?', (test_session_id,))
            result = cursor.fetchone()
            
            if result:
                # 清理测试数据
                cursor.execute('DELETE FROM recording_sessions WHERE session_id = ?', (test_session_id,))
                conn.commit()
                self.log_test_result("数据库读写测试", True, "数据库读写功能正常")
            else:
                self.log_test_result("数据库读写测试", False, "数据库写入失败")
                conn.close()
                return False
            
            conn.close()
            return True
            
        except Exception as e:
            self.log_test_result("数据库连接", False, f"数据库连接失败: {e}")
            return False
    
    def test_file_system(self) -> bool:
        """测试文件系统"""
        logger.info("=== 测试文件系统 ===")
        
        # 检查录制目录
        recording_dir = Path(self.config['recording_dir'])
        if not recording_dir.exists():
            try:
                recording_dir.mkdir(parents=True, exist_ok=True)
                self.log_test_result("录制目录创建", True, f"录制目录已创建: {recording_dir}")
            except Exception as e:
                self.log_test_result("录制目录创建", False, f"无法创建录制目录: {e}")
                return False
        else:
            self.log_test_result("录制目录检查", True, f"录制目录存在: {recording_dir}")
        
        # 测试文件写入权限
        test_file = recording_dir / "test_write.tmp"
        try:
            with open(test_file, 'w') as f:
                f.write("test content")
            
            # 测试读取
            with open(test_file, 'r') as f:
                content = f.read()
            
            if content == "test content":
                self.log_test_result("文件读写权限", True, "文件读写权限正常")
            else:
                self.log_test_result("文件读写权限", False, "文件读取内容不匹配")
                return False
            
            # 清理测试文件
            test_file.unlink()
            
        except Exception as e:
            self.log_test_result("文件读写权限", False, f"文件读写测试失败: {e}")
            return False
        
        # 检查磁盘空间
        import shutil
        total, used, free = shutil.disk_usage(recording_dir)
        free_gb = free / (1024**3)
        
        if free_gb < 1:
            self.log_test_result("磁盘空间检查", False, f"磁盘空间不足: {free_gb:.1f}GB")
            return False
        else:
            self.log_test_result("磁盘空间检查", True, f"磁盘空间充足: {free_gb:.1f}GB")
        
        return True
    
    def simulate_webrtc_stream(self) -> bool:
        """模拟WebRTC流推送"""
        logger.info("=== 模拟WebRTC流推送 ===")
        
        # 使用FFmpeg模拟推流
        try:
            # 生成测试视频流
            ffmpeg_cmd = [
                'ffmpeg',
                '-f', 'lavfi',
                '-i', 'testsrc=duration=30:size=640x480:rate=25',
                '-f', 'lavfi',
                '-i', 'sine=frequency=1000:duration=30',
                '-c:v', 'libx264',
                '-preset', 'ultrafast',
                '-tune', 'zerolatency',
                '-c:a', 'aac',
                '-f', 'rtmp',
                f'rtmp://localhost:1935/{self.config["test_app"]}/{self.config["test_stream"]}'
            ]
            
            logger.info("开始推送测试流...")
            process = subprocess.Popen(
                ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待流建立
            time.sleep(5)
            
            # 检查流是否存在
            try:
                response = requests.get(f"{self.config['srs_api']}/streams", timeout=5)
                if response.status_code == 200:
                    streams = response.json().get('streams', [])
                    test_stream_found = any(
                        stream.get('app') == self.config['test_app'] and 
                        stream.get('name') == self.config['test_stream']
                        for stream in streams
                    )
                    
                    if test_stream_found:
                        self.log_test_result("流推送建立", True, "测试流已成功建立")
                        
                        # 等待录制
                        logger.info(f"等待录制 {self.config['test_duration']} 秒...")
                        time.sleep(self.config['test_duration'])
                        
                        # 停止推流
                        process.terminate()
                        process.wait(timeout=10)
                        
                        self.log_test_result("流推送完成", True, f"测试流推送 {self.config['test_duration']} 秒")
                        return True
                    else:
                        self.log_test_result("流推送建立", False, "测试流未在SRS中找到")
                        process.terminate()
                        return False
                else:
                    self.log_test_result("流状态查询", False, f"无法查询流状态: {response.status_code}")
                    process.terminate()
                    return False
                    
            except Exception as e:
                self.log_test_result("流状态查询", False, f"查询流状态失败: {e}")
                process.terminate()
                return False
                
        except FileNotFoundError:
            self.log_test_result("FFmpeg可用性", False, "FFmpeg未安装或不在PATH中")
            return False
        except Exception as e:
            self.log_test_result("流推送模拟", False, f"模拟推流失败: {e}")
            return False
    
    def test_recording_functionality(self) -> bool:
        """测试录制功能"""
        logger.info("=== 测试录制功能 ===")
        
        # 等待录制文件生成
        time.sleep(5)
        
        # 查找录制文件
        recording_pattern = f"{self.config['test_app']}"
        recording_files = []
        
        for root, dirs, files in os.walk(self.config['recording_dir']):
            for file in files:
                if file.endswith('.mp4') and recording_pattern in root:
                    recording_files.append(os.path.join(root, file))
        
        if not recording_files:
            self.log_test_result("录制文件生成", False, "未找到录制文件")
            return False
        
        # 检查最新的录制文件
        latest_file = max(recording_files, key=os.path.getctime)
        file_size = os.path.getsize(latest_file)
        
        if file_size == 0:
            self.log_test_result("录制文件内容", False, f"录制文件为空: {latest_file}")
            return False
        
        self.log_test_result("录制文件生成", True, f"录制文件已生成: {latest_file} ({file_size} bytes)")
        
        # 验证文件格式
        try:
            # 使用ffprobe检查文件
            ffprobe_cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                latest_file
            ]
            
            result = subprocess.run(ffprobe_cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                probe_data = json.loads(result.stdout)
                
                # 检查视频流
                video_streams = [s for s in probe_data.get('streams', []) if s.get('codec_type') == 'video']
                audio_streams = [s for s in probe_data.get('streams', []) if s.get('codec_type') == 'audio']
                
                if video_streams:
                    video_codec = video_streams[0].get('codec_name', 'unknown')
                    self.log_test_result("视频流验证", True, f"视频编码: {video_codec}")
                else:
                    self.log_test_result("视频流验证", False, "录制文件中无视频流")
                    return False
                
                if audio_streams:
                    audio_codec = audio_streams[0].get('codec_name', 'unknown')
                    self.log_test_result("音频流验证", True, f"音频编码: {audio_codec}")
                else:
                    self.log_test_result("音频流验证", False, "录制文件中无音频流")
                
                # 检查时长
                duration = float(probe_data.get('format', {}).get('duration', 0))
                if duration > 0:
                    self.log_test_result("录制时长验证", True, f"录制时长: {duration:.1f}秒")
                else:
                    self.log_test_result("录制时长验证", False, "无法获取录制时长")
                
                return True
                
            else:
                self.log_test_result("文件格式验证", False, f"ffprobe检查失败: {result.stderr}")
                return False
                
        except FileNotFoundError:
            self.log_test_result("FFprobe可用性", False, "ffprobe未安装")
            return False
        except Exception as e:
            self.log_test_result("文件格式验证", False, f"文件格式验证失败: {e}")
            return False
    
    def test_database_records(self) -> bool:
        """测试数据库记录"""
        logger.info("=== 测试数据库记录 ===")
        
        try:
            conn = sqlite3.connect(self.config['db_path'])
            cursor = conn.cursor()
            
            # 查找测试会话记录
            cursor.execute('''
                SELECT * FROM recording_sessions 
                WHERE app = ? AND stream = ?
                ORDER BY created_at DESC
                LIMIT 1
            ''', (self.config['test_app'], self.config['test_stream']))
            
            session_record = cursor.fetchone()
            
            if session_record:
                self.log_test_result("会话记录查询", True, "找到测试会话记录")
                
                # 检查记录字段
                session_id = session_record[1]  # 假设session_id是第二个字段
                
                # 查找文件记录
                cursor.execute('''
                    SELECT * FROM recording_files 
                    WHERE session_id = ?
                ''', (session_id,))
                
                file_records = cursor.fetchall()
                
                if file_records:
                    self.log_test_result("文件记录查询", True, f"找到 {len(file_records)} 个文件记录")
                else:
                    self.log_test_result("文件记录查询", False, "未找到文件记录")
                    conn.close()
                    return False
                
            else:
                self.log_test_result("会话记录查询", False, "未找到测试会话记录")
                conn.close()
                return False
            
            conn.close()
            return True
            
        except Exception as e:
            self.log_test_result("数据库记录测试", False, f"数据库记录测试失败: {e}")
            return False
    
    def test_api_endpoints(self) -> bool:
        """测试API端点"""
        logger.info("=== 测试API端点 ===")
        
        # 测试录制管理API
        endpoints = [
            ('/api/v1/sessions', 'GET', '会话列表API'),
            ('/api/v1/stats', 'GET', '统计信息API'),
            ('/api/v1/health', 'GET', '健康检查API')
        ]
        
        for endpoint, method, description in endpoints:
            try:
                url = f"{self.config['recording_api']}{endpoint}"
                response = requests.request(method, url, timeout=5)
                
                if response.status_code == 200:
                    self.log_test_result(f"API测试: {description}", True, f"响应正常: {response.status_code}")
                else:
                    self.log_test_result(f"API测试: {description}", False, f"响应异常: {response.status_code}")
                    
            except Exception as e:
                self.log_test_result(f"API测试: {description}", False, f"请求失败: {e}")
        
        return True
    
    def cleanup_test_data(self):
        """清理测试数据"""
        logger.info("=== 清理测试数据 ===")
        
        try:
            # 清理录制文件
            recording_dir = Path(self.config['recording_dir'])
            test_app_dir = recording_dir / self.config['test_app']
            
            if test_app_dir.exists():
                import shutil
                shutil.rmtree(test_app_dir)
                self.log_test_result("清理录制文件", True, f"已删除测试录制目录: {test_app_dir}")
            
            # 清理数据库记录
            conn = sqlite3.connect(self.config['db_path'])
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM recording_files WHERE session_id IN (SELECT session_id FROM recording_sessions WHERE app = ?)', (self.config['test_app'],))
            cursor.execute('DELETE FROM recording_sessions WHERE app = ?', (self.config['test_app'],))
            
            conn.commit()
            conn.close()
            
            self.log_test_result("清理数据库记录", True, "已删除测试数据库记录")
            
        except Exception as e:
            self.log_test_result("清理测试数据", False, f"清理失败: {e}")
    
    def run_full_test(self) -> bool:
        """运行完整测试"""
        self.start_time = datetime.now()
        logger.info(f"开始SRS医疗录制系统完整测试 - {self.start_time}")
        
        # 测试步骤
        test_steps = [
            ("服务连接性测试", self.test_service_connectivity),
            ("数据库连接测试", self.test_database_connectivity),
            ("文件系统测试", self.test_file_system),
            ("WebRTC流推送测试", self.simulate_webrtc_stream),
            ("录制功能测试", self.test_recording_functionality),
            ("数据库记录测试", self.test_database_records),
            ("API端点测试", self.test_api_endpoints)
        ]
        
        all_passed = True
        
        for step_name, test_func in test_steps:
            logger.info(f"\n开始执行: {step_name}")
            try:
                result = test_func()
                if not result:
                    all_passed = False
                    logger.error(f"{step_name} 失败")
            except Exception as e:
                logger.error(f"{step_name} 异常: {e}")
                self.log_test_result(step_name, False, f"测试异常: {e}")
                all_passed = False
        
        # 清理测试数据
        self.cleanup_test_data()
        
        self.end_time = datetime.now()
        duration = (self.end_time - self.start_time).total_seconds()
        
        logger.info(f"\n测试完成 - 耗时: {duration:.1f}秒")
        
        return all_passed
    
    def generate_test_report(self) -> str:
        """生成测试报告"""
        if not self.test_results:
            return "无测试结果"
        
        passed_tests = [r for r in self.test_results if r['success']]
        failed_tests = [r for r in self.test_results if not r['success']]
        
        report = f"""
SRS医疗录制系统测试报告
========================

测试时间: {self.start_time} - {self.end_time}
测试耗时: {(self.end_time - self.start_time).total_seconds():.1f}秒

测试概览:
- 总测试数: {len(self.test_results)}
- 通过测试: {len(passed_tests)}
- 失败测试: {len(failed_tests)}
- 成功率: {len(passed_tests)/len(self.test_results)*100:.1f}%

通过的测试:
"""
        
        for test in passed_tests:
            report += f"✓ {test['test_name']}: {test['message']}\n"
        
        if failed_tests:
            report += "\n失败的测试:\n"
            for test in failed_tests:
                report += f"✗ {test['test_name']}: {test['message']}\n"
        
        report += f"\n详细结果已保存到: {self.srs_medical_dir}/test_results.json"
        
        # 保存详细结果
        with open(self.srs_medical_dir / "test_results.json", 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        return report

def main():
    """命令行入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SRS医疗录制系统测试工具')
    parser.add_argument('--test-duration', type=int, default=30, help='测试流持续时间（秒）')
    parser.add_argument('--cleanup-only', action='store_true', help='仅清理测试数据')
    parser.add_argument('--report-only', action='store_true', help='仅生成测试报告')
    
    args = parser.parse_args()
    
    tester = SRSMedicalTester()
    tester.config['test_duration'] = args.test_duration
    
    if args.cleanup_only:
        tester.cleanup_test_data()
        return
    
    if args.report_only:
        # 加载之前的测试结果
        try:
            with open(tester.srs_medical_dir / "test_results.json", 'r', encoding='utf-8') as f:
                tester.test_results = json.load(f)
            print(tester.generate_test_report())
        except FileNotFoundError:
            print("未找到测试结果文件")
        return
    
    # 运行完整测试
    success = tester.run_full_test()
    
    # 生成报告
    report = tester.generate_test_report()
    print(report)
    
    # 保存报告
    with open(tester.srs_medical_dir / "test_report.txt", 'w', encoding='utf-8') as f:
        f.write(report)
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
