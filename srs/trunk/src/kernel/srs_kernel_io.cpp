//
// Copyright (c) 2013-2024 The SRS Authors
//
// SPDX-License-Identifier: MIT
//

#include <srs_kernel_io.hpp>

ISrsReader::ISrsReader()
{
}

ISrsReader::~ISrsReader()
{
}

ISrsSeeker::ISrsSeeker()
{
}

ISrsSeeker::~ISrsSeeker()
{
}

ISrsReadSeeker::ISrsReadSeeker()
{
}

ISrsReadSeeker::~ISrsReadSeeker()
{
}

ISrsStreamWriter::ISrsStreamWriter()
{
}

ISrsStreamWriter::~ISrsStreamWriter()
{
}

ISrsVectorWriter::ISrsVectorWriter()
{
}

ISrsVectorWriter::~ISrsVectorWriter()
{
}

ISrsWriter::ISrsWriter()
{
}

ISrsWriter::~ISrsWriter()
{
}

ISrsWriteSeeker::ISrsWriteSeeker()
{
}

ISrsWriteSeeker::~ISrsWriteSeeker()
{
}

