# All configurations for SRS, you can find whatever you want about configuration of SRS.
# Note that please never use this file as configuration of SRS, because it's just a full set of configurations.

#############################################################################################
# Global sections
#############################################################################################
# Config file, specified by cli such as `-c conf/srs.conf`.
# Overwrite by env SRS_CONFIG_FILE

# The id of server, for stat and api identification.
# Note that SRS will generate a random id if not configured.
# Overwrite by env SRS_SERVER_ID
server_id srs-ie193id;

# The pid file to write the pid, for managing the SRS process and avoiding duplicated processes.
# If need to run multiple processes, please change this pid file to another one.
# Note: Do not support reloading, for SRS5+
# Overwrite by env SRS_PID
# Default: ./objs/srs.pid
pid ./objs/srs.pid;
# the log dir for FFMPEG.
# if enabled ffmpeg, each transcoding stream will create a log file.
# /dev/null to disable the log.
# Note: Do not support reloading, for SRS5+
# Overwrite by env SRS_FF_LOG_DIR
# default: ./objs
ff_log_dir ./objs;
# the log level for FFMPEG.
#       info warning error fatal panic quiet
#       trace debug verbose
# Note: Do not support reloading, for SRS5+
# Overwrite by env SRS_FF_LOG_LEVEL
# default: info
ff_log_level info;
# the log tank, console or file.
# if console, print log to console.
# if file, write log to file. requires srs_log_file if log to file.
# Note: Do not support reloading, for SRS5+
# Overwrite by env SRS_LOG_TANK or SRS_SRS_LOG_TANK
# default: file.
srs_log_tank console;
# The log level for logging to console or file. It can be:
#       verbose, info, trace, warn, error
# If configure --log-level_v2=off, use SRS 4.0 level specs which is v1, the level text is:
#       Verb, Info, Trace, Warn, Error
# If configure --log-level_v2=on, use SRS 5.0 level specs which is v2, the level text is:
#       TRACE, DEBUG, INFO, WARN, ERROR
# Note: Do not support reloading, for SRS5+
# Overwrite by env SRS_LOG_LEVEL or SRS_SRS_LOG_LEVEL
# default: trace
srs_log_level trace;
# The log level v2, rewrite the config srs_log_level if not empty, it can be:
#       trace, debug, info, warn, error
# If configure --log-level_v2=off, use SRS 4.0 level specs which is v1, the level text is:
#       Verb, Info, Trace, Warn, Error
# If configure --log-level_v2=on, use SRS 5.0 level specs which is v2, the level text is:
#       TRACE, DEBUG, INFO, WARN, ERROR
# Overwrite by env SRS_LOG_LEVEL_V2 or SRS_SRS_LOG_LEVEL_V2
srs_log_level_v2 info;
# when srs_log_tank is file, specifies the log file.
# Note: Do not support reloading, for SRS5+
# Overwrite by env SRS_LOG_FILE or SRS_SRS_LOG_FILE
# default: ./objs/srs.log
srs_log_file ./objs/srs.log;
# the max connections.
# if exceed the max connections, server will drop the new connection.
# Overwrite by env SRS_MAX_CONNECTIONS
# default: 1000
max_connections 1000;
# whether start as daemon
# @remark: do not support reload.
# Overwrite by env SRS_DAEMON
# default: on
daemon off;
# whether use utc_time to generate the time struct,
# if off, use localtime() to generate it,
# if on, use gmtime() instead, which use UTC time.
# Note: Do not support reloading, for SRS5+
# Overwrite by env SRS_UTC_TIME
# default: off
utc_time off;
# config for the pithy print in ms,
# which always print constant message specified by interval,
# whatever the clients in concurrency.
# Overwrite by env SRS_PITHY_PRINT_MS
# default: 10000
pithy_print_ms 10000;

# the work dir for server, to chdir(work_dir) when not empty or "./"
# user can config this directory to change the dir.
# @reamrk do not support reload.
# Overwrite by env SRS_WORK_DIR
# default: ./
work_dir ./;
# whether quit when parent process changed,
# used for supervisor mode(not daemon), srs should always quit when
# supervisor process exited.
# @remark conflict with daemon, error when both daemon and asprocess are on.
# @reamrk do not support reload.
# Overwrite by env SRS_ASPROCESS
# default: off
asprocess off;
# Whether client empty IP is ok, for example, health checking by SLB.
# If ok(on), we will ignore this connection without warnings or errors.
# Overwrite by env SRS_EMPTY_IP_OK
# default: on
empty_ip_ok on;

# Whether in docker. When SRS starting, it will detect the docker, however
# it might detect failed, then read this config.
# Overwrite by env SRS_IN_DOCKER
# Default: off
in_docker off;
# For gracefully quit, wait for a while then close listeners,
# because K8S notify SRS with SIGQUIT and update Service simultaneously,
# maybe there is some new connections incoming before Service updated.
# @see https://github.com/ossrs/srs/issues/1595#issuecomment-587516567
# Overwrite by env SRS_GRACE_START_WAIT
# default: 2300
grace_start_wait 2300;
# For gracefully quit, final wait for cleanup in milliseconds.
# @see https://github.com/ossrs/srs/issues/1579#issuecomment-587414898
# Overwrite by env SRS_GRACE_FINAL_WAIT
# default: 3200
grace_final_wait 3200;
# Whether force gracefully quit, never fast quit.
# By default, SIGTERM which means fast quit, is sent by K8S, so we need to
# force SRS to treat SIGTERM as gracefully quit for gray release or canary.
# @see https://github.com/ossrs/srs/issues/1579#issuecomment-587475077
# Overwrite by env SRS_FORCE_GRACE_QUIT
# default: off
force_grace_quit off;
# Whether disable daemon for docker.
# If on, it will set daemon to off in docker, even daemon is on.
# Overwrite by env SRS_DISABLE_DAEMON_FOR_DOCKER
# default: on
disable_daemon_for_docker on;
# Whether auto reload by watching the config file by inotify.
# Overwrite by env SRS_INOTIFY_AUTO_RELOAD
# default: off
inotify_auto_reload off;
# Whether enable inotify_auto_reload for docker.
# If on, it will set inotify_auto_reload to on in docker, even it's off.
# Overwrite by env SRS_AUTO_RELOAD_FOR_DOCKER
# default: on
auto_reload_for_docker on;

#############################################################################################
# RTMP sections
#############################################################################################
# the rtmp listen ports, split by space, each listen entry is <[ip:]port>
# for example, *************:1935 ************:1935
# where the ip is optional, default to 0.0.0.0, that is 1935 equals to 0.0.0.0:1935
# Overwrite by env SRS_LISTEN
listen 1935;
# the default chunk size is 128, max is 65536,
# some client does not support chunk size change,
# however, most clients support it and it can improve
# performance about 10%.
# Overwrite by env SRS_CHUNK_SIZE
# default: 60000
chunk_size 60000;

#############################################################################################
# HTTP sections
#############################################################################################
# api of srs.
# the http api config, export for external program to manage srs.
# user can access http api of srs in browser directly, for instance, to access by:
#       curl http://*************:1985/api/v1/reload
# which will reload srs, like cmd killall -1 srs, but the js can also invoke the http api,
# where the cli can only be used in shell/terminate.
http_api {
    # whether http api is enabled.
    # Overwrite by env SRS_HTTP_API_ENABLED
    # default: off
    enabled on;
    # The http api listen entry is <[ip:]port>, For example, *************:8080, where the ip is optional, default to
    # 0.0.0.0, that is 8080 equals to 0.0.0.0:8080.
    # Note that you're able to use a dedicated port for HTTP API, such as 1985, to be different with HTTP server. In
    # this situation, you you must also set another HTTPS API port.
    # Overwrite by env SRS_HTTP_API_LISTEN
    # Default: 1985
    listen 8080;
    # whether enable crossdomain request.
    # Overwrite by env SRS_HTTP_API_CROSSDOMAIN
    # default: on
    crossdomain on;
    # the HTTP RAW API is more powerful api to change srs state and reload.
    raw_api {
        # whether enable the HTTP RAW API.
        # Overwrite by env SRS_HTTP_API_RAW_API_ENABLED
        # default: off
        enabled off;
        # whether enable rpc reload.
        # Overwrite by env SRS_HTTP_API_RAW_API_ALLOW_RELOAD
        # default: off
        allow_reload off;
        # whether enable rpc query.
        # Always off by https://github.com/ossrs/srs/issues/2653
        #allow_query off;
        # whether enable rpc update.
        # Always off by https://github.com/ossrs/srs/issues/2653
        #allow_update off;
    }
    # the auth is authentication for http api
    auth {
        # whether enable the HTTP AUTH.
        # Overwrite by env SRS_HTTP_API_AUTH_ENABLED
        # default: off
        enabled         on;
        # The username of Basic authentication:
        # Overwrite by env SRS_HTTP_API_AUTH_USERNAME
        username        admin;
        # The password of Basic authentication:
        # Overwrite by env SRS_HTTP_API_AUTH_PASSWORD
        password        admin;
    }
    # For https_api or HTTPS API.
    # Note: The SRS HTTPS server is for demo only, please use Nginx/Caddy to proxy to SRS in production environment.
    https {
        # Whether enable HTTPS API.
        # Overwrite by env SRS_HTTP_API_HTTPS_ENABLED
        # default: off
        enabled on;
        # The listen endpoint for HTTPS API.
        # Note that you're able to use a dedicated port for HTTPS API, such as 1990, and the HTTP API should not be
        # the same of HTTP server(8080) neither.
        # Overwrite by env SRS_HTTP_API_HTTPS_LISTEN
        # Default: 1990
        listen 8088;
        # The SSL private key file, generated by:
        #       openssl genrsa -out server.key 2048
        # Overwrite by env SRS_HTTP_API_HTTPS_KEY
        # default: ./conf/server.key
        key ./conf/server.key;
        # The SSL public cert file, generated by:
        #       openssl req -new -x509 -key server.key -out server.crt -days 3650 -subj "/C=CN/ST=Beijing/L=Beijing/O=Me/OU=Me/CN=ossrs.net"
        # Overwrite by env SRS_HTTP_API_HTTPS_CERT
        # default: ./conf/server.crt
        cert ./conf/server.crt;
    }
}
# embedded http server in srs.
# the http streaming config, for HLS/HDS/DASH/HTTPProgressive
# global config for http streaming, user must config the http section for each vhost.
# the embed http server used to substitute nginx in ./objs/nginx,
# for example, srs running in arm, can provides RTMP and HTTP service, only with srs installed.
# user can access the http server pages, generally:
#       curl http://*************:80/srs.html
# which will show srs version and welcome to srs.
# @remark, the http embedded stream need to config the vhost, for instance, the __defaultVhost__
# need to open the feature http of vhost.
http_server {
    # whether http streaming service is enabled.
    # Overwrite by env SRS_HTTP_SERVER_ENABLED
    # default: off
    enabled on;
    # the http streaming listen entry is <[ip:]port>
    # for example, *************:8080
    # where the ip is optional, default to 0.0.0.0, that is 8080 equals to 0.0.0.0:8080
    # @remark, if use lower port, for instance 80, user must start srs by root.
    # Overwrite by env SRS_HTTP_SERVER_LISTEN
    # default: 8080
    listen 8080;
    # the default dir for http root.
    # Overwrite by env SRS_HTTP_SERVER_DIR
    # default: ./objs/nginx/html
    dir ./objs/nginx/html;
    # whether enable crossdomain request.
    # for both http static and stream server and apply on all vhosts.
    # Overwrite by env SRS_HTTP_SERVER_CROSSDOMAIN
    # default: on
    crossdomain on;
    # For https_server or HTTPS Streaming.
    # Note: The SRS HTTPS server is for demo only, please use Nginx/Caddy to proxy to SRS in production environment.
    https {
        # Whether enable HTTPS Streaming.
        # Overwrite by env SRS_HTTP_SERVER_HTTPS_ENABLED
        # default: off
        enabled on;
        # The listen endpoint for HTTPS Streaming.
        # Overwrite by env SRS_HTTP_SERVER_HTTPS_LISTEN
        # default: 8088
        listen 8088;
        # The SSL private key file, generated by:
        #       openssl genrsa -out server.key 2048
        # Overwrite by env SRS_HTTP_SERVER_HTTPS_KEY
        # default: ./conf/server.key
        key ./conf/server.key;
        # The SSL public cert file, generated by:
        #       openssl req -new -x509 -key server.key -out server.crt -days 3650 -subj "/C=CN/ST=Beijing/L=Beijing/O=Me/OU=Me/CN=ossrs.net"
        # Overwrite by env SRS_HTTP_SERVER_HTTPS_CERT
        # default: ./conf/server.crt
        cert ./conf/server.crt;
    }
}

#############################################################################################
# SRT server section
#############################################################################################
# @doc https://github.com/ossrs/srs/issues/1147#usage
srt_server {
    # whether SRT server is enabled.
    # Overwrite by env SRS_SRT_SERVER_ENABLED
    # default: off
    enabled on;
    # The UDP listen port for SRT.
    # Overwrite by env SRS_SRT_SERVER_LISTEN
    listen 10080;
    # For detail parameters, please read wiki:
    # @see https://ossrs.net/lts/zh-cn/docs/v5/doc/srt-params
    # @see https://ossrs.io/lts/en-us/docs/v5/doc/srt-params
    # The maxbw is the max bandwidth of the sender side.
    # 	-1: Means the biggest bandwidth is infinity.
    # 	 0: Means the bandwidth is determined by SRTO_INPUTBW.
    # 	>0: Means the bandwidth is the configuration value.
    # Overwrite by env SRS_SRT_SERVER_MAXBW
    # default: -1
    maxbw 1000000000;
    # Maximum Segment Size. Used for buffer allocation and rate calculation using packet counter assuming fully
    # filled packets. Each party can set its own MSS value independently. During a handshake the parties exchange
    # MSS values, and the lowest is used.
    # Overwrite by env SRS_SRT_SERVER_MSS
    # default: 1500
    mss 1500;
    # The timeout time of the SRT connection on the sender side in ms. When SRT connects to a peer costs time 
    # more than this config, it will be close.
    # Overwrite by env SRS_SRT_SERVER_CONNECT_TIMEOUT
    # default: 3000
    connect_timeout 4000;
	# The timeout time of SRT connection on the receiver side in ms. When the SRT connection is idle 
    # more than this config, it will be close.
    # Overwrite by env SRS_SRT_SERVER_PEER_IDLE_TIMEOUT
    # default: 10000
    peer_idle_timeout 8000;
    # Default app for vmix, see https://github.com/ossrs/srs/pull/1615
    # Overwrite by env SRS_SRT_SERVER_DEFAULT_APP
    # default: live
    default_app live;
	# The peerlatency is set by the sender side and will notify the receiver side.
    # Overwrite by env SRS_SRT_SERVER_PEERLATENCY
    # default: 0
    peerlatency 0;
	# The recvlatency means latency from sender to receiver.
    # Overwrite by env SRS_SRT_SERVER_RECVLATENCY
    # default: 120
    recvlatency 0;
	# This latency configuration configures both recvlatency and peerlatency to the same value.
    # Overwrite by env SRS_SRT_SERVER_LATENCY
    # default: 120
    latency 0;
	# The tsbpd mode means timestamp based packet delivery.
	# SRT sender side will pack timestamp in each packet. If this config is true,
	# the receiver will read the packet according to the timestamp in the head of the packet.
    # Overwrite by env SRS_SRT_SERVER_TSBPDMODE
    # default: on
    tsbpdmode off;
	# The tlpkdrop means too-late Packet Drop
	# SRT sender side will pack timestamp in each packet, When the network is congested,
	# the packet will drop if latency is bigger than the configuration in both sender side and receiver side.
	# And on the sender side, it also will be dropped because latency is bigger than configuration.
    # Overwrite by env SRS_SRT_SERVER_TLPKTDROP
    # default: on
    tlpktdrop off;
	# The send buffer size of SRT.
    # Overwrite by env SRS_SRT_SERVER_SENDBUF
    # default:  8192 * (1500-28)
    sendbuf 2000000;
	# The recv buffer size of SRT.
    # Overwrite by env SRS_SRT_SERVER_RECVBUF
    # default:  8192 * (1500-28)
    recvbuf 2000000;
    # The passphrase of SRT.
    # If passphrase is no empty, all the srt client must be using the correct passphrase to publish or play,
    # or the srt connection will reject. The length of passphrase must be in range 10~79.
    # @see https://github.com/Haivision/srt/blob/master/docs/API/API-socket-options.md#srto_passphrase.
    # Overwrite by env SRS_SRT_SERVER_PASSPHRASE
    # default: ""
    passphrase xxxxxxxxxxxx;
    # The pbkeylen of SRT.
    # The pbkeylen determined the AES encrypt algorithm, this option only allow 4 values which is 0, 16, 24, 32
    # @see https://github.com/Haivision/srt/blob/master/docs/API/API-socket-options.md#srto_pbkeylen.
    # Overwrite by env SRS_SRT_SERVER_PBKEYLEN
    # default: 0
    pbkeylen 16;
}

vhost srt.vhost.srs.com {
    srt {
        # Whether enable SRT on this vhost.
        # Overwrite by env SRS_VHOST_SRT_ENABLED for all vhosts.
        # Default: off
        enabled on;
        # Whether covert SRT to RTMP stream.
        # Overwrite by env SRS_VHOST_SRT_TO_RTMP for all vhosts.
        # Default: on
        srt_to_rtmp on;
    }
}

#############################################################################################
# WebRTC server section
#############################################################################################
rtc_server {
    # Whether enable WebRTC server.
    # Overwrite by env SRS_RTC_SERVER_ENABLED
    # default: off
    enabled on;
    # The udp listen port, we will reuse it for connections.
    # Overwrite by env SRS_RTC_SERVER_LISTEN
    # default: 8000
    listen 8000;
    # For WebRTC over TCP directly, not TURN, see https://github.com/ossrs/srs/issues/2852
    # Some network does not support UDP, or not very well, so we use TCP like HTTP/80 port for firewall traversing.
    tcp {
        # Whether enable WebRTC over TCP.
        # Overwrite by env SRS_RTC_SERVER_TCP_ENABLED
        # Default: off
        enabled off;
        # The TCP listen port for WebRTC. Highly recommend is some normally used ports, such as TCP/80, TCP/443,
        # TCP/8000, TCP/8080 etc. However SRS default to TCP/8000 corresponding to UDP/8000.
        # Overwrite by env SRS_RTC_SERVER_TCP_LISTEN
        # Default: 8000
        listen 8000;
    }
    # The protocol for candidate to use, it can be:
    #       udp         Generate UDP candidates. Note that UDP server is always enabled for WebRTC.
    #       tcp         Generate TCP candidates. Fail if rtc_server.tcp(WebRTC over TCP) is disabled.
    #       all         Generate UDP+TCP candidates. Ignore if rtc_server.tcp(WebRTC over TCP) is disabled.
    # Note that if both are connected, we will use the first connected(DTLS done) one.
    # Overwrite by env SRS_RTC_SERVER_PROTOCOL
    # Default: udp
    protocol udp;
    # The exposed candidate IPs, response in SDP candidate line. It can be:
    #       *           Retrieve server IP automatically, from all network interfaces.
    #       $CANDIDATE  Read the IP from ENV variable, use * if not set.
    #       x.x.x.x     A specified IP address or DNS name, use * if 0.0.0.0.
    # You can also set the candidate by the query string eip, note that you can also set the UDP port,
    # for example:
    #       http://locahost:1985/rtc/v1/whip/?app=live&stream=livestream&eip=************
    #       http://locahost:1985/rtc/v1/whip/?app=live&stream=livestream&eip=************:18000
    # @remark For Firefox, the candidate MUST be IP, MUST NOT be DNS name, see https://bugzilla.mozilla.org/show_bug.cgi?id=1239006
    # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/webrtc#config-candidate
    # Overwrite by env SRS_RTC_SERVER_CANDIDATE
    # default: *
    candidate *;
    # If candidate is * or 0.0.0.0, means SRS could detect IP automatically, filtered by ip_family.
    # You can config this to off to disable the detecting, then SRS will try to parse the API hostname.
    # Note that browser might fail if no CANDIDATE specified.
    # Overwrite by env SRS_RTC_SERVER_USE_AUTO_DETECT_NETWORK_IP
    # Default: on
    use_auto_detect_network_ip on;
    # The IP family filter for auto discover candidate, it can be:
    #       ipv4        Filter IP v4 candidates.
    #       ipv6        Filter IP v6 candidates.
    #       all         Filter all IP v4 or v6 candidates.
    # For example, if set to ipv4, we only use the IPv4 address as candidate.
    # Overwrite by env SRS_RTC_SERVER_IP_FAMILY
    # default: ipv4
    ip_family ipv4;
    # If api_as_candidates is on, SRS would try to use the IP of api server, specified by srs.sdk.js request:
    #       api:string "http://r.ossrs.net:1985/rtc/v1/play/"
    # in this case, the r.ossrs.net and ************** will be added as candidates.
    # Overwrite by env SRS_RTC_SERVER_API_AS_CANDIDATES
    # Default: on
    api_as_candidates on;
    # If use api as CANDIDATE, whether resolve the api hostname.
    # Note that use original domain name as CANDIDATE, which might make Firefox failed, see https://bugzilla.mozilla.org/show_bug.cgi?id=1239006
    # Note that if hostname is IPv4 address, always directly use it.
    # Overwrite by env SRS_RTC_SERVER_RESOLVE_API_DOMAIN
    # Default: on
    resolve_api_domain on;
    # If use api as CANDIDATE, whether keep original api domain name as CANDIDATE.
    # Note that use original domain name as CANDIDATE, which might make Firefox failed, see https://bugzilla.mozilla.org/show_bug.cgi?id=1239006
    # Overwrite by env SRS_RTC_SERVER_KEEP_API_DOMAIN
    # Default: off
    keep_api_domain off;
    # Whether use ECDSA certificate.
    # If not, use RSA certificate.
    # Overwrite by env SRS_RTC_SERVER_ECDSA
    # default: on
    ecdsa on;
    # Whether encrypt RTP packet by SRTP.
    # @remark Should always turn it on, or Chrome will fail.
    # Overwrite by env SRS_RTC_SERVER_ENCRYPT
    # default: on
    encrypt on;
    # We listen multiple times at the same port, by REUSEPORT, to increase the UDP queue.
    # Note that you can set to 1 and increase the system UDP buffer size by net.core.rmem_max
    # and net.core.rmem_default or just increase this to get larger UDP recv and send buffer.
    # Overwrite by env SRS_RTC_SERVER_REUSEPORT
    # default: 1
    reuseport 1;
    # Whether merge multiple NALUs into one.
    # @see https://github.com/ossrs/srs/issues/307#issuecomment-612806318
    # Overwrite by env SRS_RTC_SERVER_MERGE_NALUS
    # default: off
    merge_nalus off;
    # The black-hole to copy packet to, for debugging.
    # For example, when debugging Chrome publish stream, the received packets are encrypted cipher,
    # we can set the publisher black-hole, SRS will copy the plaintext packets to black-hole, and
    # we are able to capture the plaintext packets by wireshark.
    black_hole {
        # Whether enable the black-hole.
        # Overwrite by env SRS_RTC_SERVER_BLACK_HOLE_ENABLED
        # default: off
        enabled off;
        # The black-hole address for session.
        # Overwrite by env SRS_RTC_SERVER_BLACK_HOLE_ADDR
        addr 127.0.0.1:10000;
    }
}

vhost rtc.vhost.srs.com {
    rtc {
        # Whether enable WebRTC server.
        # Overwrite by env SRS_VHOST_RTC_ENABLED for all vhosts.
        # default: off
        enabled on;
        # Whether support NACK.
        # Overwrite by env SRS_VHOST_RTC_NACK for all vhosts.
        # default: on
        nack on;
        # Whether directly use the packet, avoid copy.
        # Overwrite by env SRS_VHOST_RTC_NACK_NO_COPY for all vhosts.
        # default: on
        nack_no_copy on;
        # Whether support TWCC.
        # Overwrite by env SRS_VHOST_RTC_TWCC for all vhosts.
        # default: on
        twcc on;
        # The timeout in seconds for session timeout.
        # Client will send ping(STUN binding request) to server, we use it as heartbeat.
        # Overwrite by env SRS_VHOST_RTC_STUN_TIMEOUT for all vhosts.
        # default: 30
        stun_timeout 30;
        # The strict check when process stun.
        # Overwrite by env SRS_VHOST_RTC_STUN_STRICT_CHECK for all vhosts.
        # default: off
        stun_strict_check on;
        # The role of dtls when peer is actpass: passive or active
        # Overwrite by env SRS_VHOST_RTC_DTLS_ROLE for all vhosts.
        # default: passive
        dtls_role passive;
        # The version of dtls, support dtls1.0, dtls1.2, and auto
        # Overwrite by env SRS_VHOST_RTC_DTLS_VERSION for all vhosts.
        # default: auto
        dtls_version auto;
        # Drop the packet with the pt(payload type), 0 never drop.
        # Overwrite by env SRS_VHOST_RTC_DROP_FOR_PT for all vhosts.
        # default: 0
        drop_for_pt 0;
        ###############################################################
        # Whether enable transmuxing RTMP to RTC.
        # If enabled, transcode aac to opus.
        # Overwrite by env SRS_VHOST_RTC_RTMP_TO_RTC for all vhosts.
        # default: off
        rtmp_to_rtc off;
        # Whether keep B-frame, which is normal feature in live streaming,
        # but usually disabled in RTC.
        # Overwrite by env SRS_VHOST_RTC_KEEP_BFRAME for all vhosts.
        # default: off
        keep_bframe off;
        # Whether to keep the h.264 SEI type NALU packet.
        # DJI drone M30T will send many SEI type NALU packet, while iOS hardware decoder (Video Toolbox)
        # dislike to feed it so many SEI NALU between NonIDR and IDR NALU packets.
        # @see https://github.com/ossrs/srs/issues/4052
        # Overwrite by env SRS_VHOST_RTC_KEEP_AVC_NALU_SEI for all vhosts.
        # Default: on
        keep_avc_nalu_sei on;
        # The transcode audio bitrate, for RTMP to RTC.
        # Overwrite by env SRS_VHOST_RTC_OPUS_BITRATE for all vhosts.
        # [8000, 320000]
        # default: 48000
        opus_bitrate 48000;
        ###############################################################
        # Whether enable transmuxing RTC to RTMP.
        # Overwrite by env SRS_VHOST_RTC_RTC_TO_RTMP for all vhosts.
        # Default: off
        rtc_to_rtmp off;
        # The PLI interval in seconds, for RTC to RTMP.
        # Note the available range is [0.5, 30]
        # Overwrite by env SRS_VHOST_RTC_PLI_FOR_RTMP for all vhosts.
        # Default: 6.0
        pli_for_rtmp 6.0;
        # The transcode audio bitrate, for RTC to RTMP.
        # Overwrite by env SRS_VHOST_RTC_AAC_BITRATE for all vhosts.
        # [8000, 320000]
        # default: 48000
        aac_bitrate 48000;
    }
    ###############################################################
    # For transmuxing RTMP to RTC, it will impact the default values if RTC is on.
    # Whether enable min delay mode for vhost.
    # Overwrite by env SRS_VHOST_MIN_LATENCY for all vhosts.
    # default: on, for RTC.
    min_latency on;
    play {
        # set the MW(merged-write) latency in ms.
        # @remark For WebRTC, we enable pass-by-timestamp mode, so we ignore this config.
        # Overwrite by env SRS_VHOST_PLAY_MW_LATENCY for all vhosts.
        # default: 0 (For WebRTC)
        mw_latency 0;
        # Set the MW(merged-write) min messages.
        # default: 0 (For Real-Time, that is min_latency on)
        # default: 1 (For WebRTC, that is min_latency off)
        # Overwrite by env SRS_VHOST_PLAY_MW_MSGS for all vhosts.
        mw_msgs 0;
    }
}

#############################################################################################
# Stream converter sections
#############################################################################################
# The stream converter coverts stream from other protocol to SRS over RTMP.
# @see https://github.com/ossrs/srs/tree/develop#stream-architecture

# Push MPEGTS over UDP to SRS.
stream_caster {
    # Whether stream converter is enabled.
    # Default: off
    enabled on;
    # The type of stream converter, could be:
    #       mpegts_over_udp, push MPEG-TS over UDP and convert to RTMP.
    caster mpegts_over_udp;
    # The output rtmp url.
    # For mpegts_over_udp converter, the typically output url:
    #           rtmp://127.0.0.1/live/livestream
    output rtmp://127.0.0.1/live/livestream;
    # The listen port for stream converter.
    # For mpegts_over_udp converter, listen at udp port. for example, 8935.
    listen 8935;
}

# Push FLV by HTTP POST to SRS.
stream_caster {
    # Whether stream converter is enabled.
    # Default: off
    enabled on;
    # The type of stream converter, could be:
    #       flv, push FLV by HTTP POST and convert to RTMP.
    caster flv;
    # The output rtmp url.
    # For flv converter, the typically output url:
    #           rtmp://127.0.0.1/[app]/[stream]
    # For example, POST to url:
    #           http://127.0.0.1:8936/live/livestream.flv
    # Where the [app] is "live" and [stream] is "livestream", output is:
    #           rtmp://127.0.0.1/live/livestream
    output rtmp://127.0.0.1/[app]/[stream];
    # The listen port for stream converter.
    # For flv converter, listen at tcp port. for example, 8936.
    listen 8936;
}

# For GB28181 server, see https://github.com/ossrs/srs/issues/3176
# For SIP specification, see https://www.ietf.org/rfc/rfc3261.html
# For GB28181 2016 spec, see https://openstd.samr.gov.cn/bzgk/gb/newGbInfo?hcno=469659DC56B9B8187671FF08748CEC89
stream_caster {
    # Whether stream converter is enabled.
    # Default: off
    enabled off;
    # The type of stream converter, could be:
    #       gb28181, Push GB28181 stream and convert to RTMP.
    caster gb28181;
    # The output rtmp url.
    # For gb28181 converter, the typically output url:
    #           rtmp://127.0.0.1/live/[stream]
    # The available variables:
    #           [stream] The video channel codec ID.
    output rtmp://127.0.0.1/live/[stream];
    # The listen TCP port for stream converter.
    #       For gb28181 converter, listen at TCP port. for example, 9000.
    # @remark We always enable bundle for media streams at this port.
    listen 9000;
    # SIP server for GB28181. Please note that this is only a demonstrated SIP server, please never use it in your
    # online production environment. Instead please use [jsip](https://github.com/usnistgov/jsip) and there is a demo
    # [srs-sip](https://github.com/ossrs/srs-sip) also base on it.
    sip {
        # Whether enable embedded SIP server.
        # Default: on
        enabled on;
        # The SIP listen port, for TCP protocol.
        # Default: 5060
        listen 5060;
        # The SIP or media transport timeout in seconds.
        # Default: 60
        timeout 60;
        # When media disconnect, the wait time in seconds to re-invite device to publish. During this wait time, device
        # might send bye or unregister message(expire is 0), so that we will cancel the re-invite.
        # Default: 5
        reinvite 5;
        # The exposed candidate IPs, response in SDP connection line. It can be:
        #       *           Retrieve server IP automatically, from all network interfaces.
        #       $CANDIDATE  Read the IP from ENV variable, use * if not set.
        #       x.x.x.x     A specified IP address or DNS name, use * if 0.0.0.0.
        # Default: *
        candidate *;
    }
}

#############################################################################################
# other global sections
#############################################################################################
# For tcmalloc, the release rate.
# @see https://gperftools.github.io/gperftools/tcmalloc.html
# @remark Should run configure --with-gperf
# Overwrite by env SRS_TCMALLOC_RELEASE_RATE
# default: 0.8
tcmalloc_release_rate 0.8;

# Query the latest available version of SRS, write a log to notice user to upgrade.
# @see https://github.com/ossrs/srs/issues/2424
# @see https://github.com/ossrs/srs/issues/2508
# Overwrite by env SRS_QUERY_LATEST_VERSION
# Default: off
query_latest_version off;

# First wait when qlv(query latest version), in seconds.
# Only available when qlv is enabled.
# Overwrite by env SRS_FIRST_WAIT_FOR_QLV
# Default: 300
first_wait_for_qlv 300;

# For thread pool.
threads {
    # The thread pool manager cycle interval, in seconds.
    # Overwrite by env SRS_THREADS_INTERVAL
    # Default: 5
    interval 5;
}

# For system circuit breaker.
circuit_breaker {
    # Whether enable the circuit breaker.
    # Overwrite by env SRS_CIRCUIT_BREAKER_ENABLED
    # Default: on
    enabled on;
    # The CPU percent(0, 100) ever 1s, as system high water-level, which enable the circuit-break
    # mechanism, for example, NACK will be disabled if high water-level.
    # Overwrite by env SRS_CIRCUIT_BREAKER_HIGH_THRESHOLD
    # Default: 90
    high_threshold 90;
    # Reset the high water-level, if number of pulse under high_threshold.
    # @remark 0 to disable the high water-level.
    # Overwrite by env SRS_CIRCUIT_BREAKER_HIGH_PULSE
    # Default: 2
    high_pulse 2;
    # The CPU percent(0, 100) ever 1s, as system critical water-level, which enable the circuit-break
    # mechanism, for example, TWCC will be disabled if high water-level.
    # @note All circuit-break mechanism of high-water-level scope are enabled in critical.
    # Overwrite by env SRS_CIRCUIT_BREAKER_CRITICAL_THRESHOLD
    # Default: 95
    critical_threshold 95;
    # Reset the critical water-level, if number of pulse under critical_threshold.
    # @remark 0 to disable the critical water-level.
    # Overwrite by env SRS_CIRCUIT_BREAKER_CRITICAL_PULSE
    # Default: 1
    critical_pulse 1;
    # If dying, also drop packets for players.
    # Overwrite by env SRS_CIRCUIT_BREAKER_DYING_THRESHOLD
    # Default: 99
    dying_threshold 99;
    # If CPU exceed the dying_pulse times, enter dying.
    # @remark 0 to disable the dying water-level.
    # Overwrite by env SRS_CIRCUIT_BREAKER_DYING_PULSE
    # Default: 5
    dying_pulse 5;
}

# TencentCloud CLS(Cloud Log Service) config, logging to cloud.
# See https://cloud.tencent.com/document/product/614/11254
tencentcloud_cls {
    # Whether CLS is enabled.
    # Overwrite by env SRS_TENCENTCLOUD_CLS_ENABLED
    # default: off
    enabled off;
    # The logging label to category the cluster servers.
    # Overwrite by env SRS_TENCENTCLOUD_CLS_LABEL
    label cn-beijing;
    # The logging tag to category the cluster servers.
    # Overwrite by env SRS_TENCENTCLOUD_CLS_TAG
    tag cn-edge;
    # The SecretId to access CLS service, see https://console.cloud.tencent.com/cam/capi
    # Overwrite by env SRS_TENCENTCLOUD_CLS_SECRET_ID
    secret_id AKIDxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;
    # The SecretKey to access CLS service, see https://console.cloud.tencent.com/cam/capi
    # Overwrite by env SRS_TENCENTCLOUD_CLS_SECRET_KEY
    secret_key xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;
    # The endpoint of CLS, format as <Region>.cls.tencentcs.com. For example:
    #       ap-guangzhou.cls.tencentcs.com
    # Note that tencentyun.com is for internal network, while tencentcs.com is for public internet.
    # See https://cloud.tencent.com/document/product/614/18940
    # Overwrite by env SRS_TENCENTCLOUD_CLS_ENDPOINT
    endpoint ap-guangzhou.cls.tencentcs.com;
    # The topic ID of CLS, see https://cloud.tencent.com/document/product/614/41035
    # Overwrite by env SRS_TENCENTCLOUD_CLS_TOPIC_ID
    topic_id xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx;
    # Whether enable logging for each log sending.
    # Overwrite by env SRS_TENCENTCLOUD_CLS_DEBUG_LOGGING
    # Default: off
    debug_logging off;
    # Whether enable the heartbeat stat every (5 * heartbeat_ratio)s.
    # Overwrite by env SRS_TENCENTCLOUD_CLS_STAT_HEARTBEAT
    # Default: on
    stat_heartbeat on;
    # Setup the heartbeat interval ratio, 1 means 5s, 2 means 10s, etc.
    # Overwrite by env SRS_TENCENTCLOUD_CLS_HEARTBEAT_RATIO
    # Default: 1
    heartbeat_ratio 1;
    # Whether enable the streams stat every (5 * streams_ratio)s.
    # Overwrite by env SRS_TENCENTCLOUD_CLS_STAT_STREAMS
    # Default: on
    stat_streams on;
    # Setup the streams interval ratio, 1 means 5s, 2 means 10s, etc.
    # Overwrite by env SRS_TENCENTCLOUD_CLS_STREAMS_RATIO
    # Default: 1
    streams_ratio 1;
}

# TencentCloud APM(Application Performance Management) config.
# See https://cloud.tencent.com/document/product/1463/57462
tencentcloud_apm {
    # Whether APM is enabled.
    # Overwrite by env SRS_TENCENTCLOUD_APM_ENABLED
    # default: off
    enabled on;
    # The APM team or business system ID, to which spans belongs to. For example, the team is apm-FsOsPOIMl (just an
    # example, not available), please get your team from https://console.cloud.tencent.com/apm/monitor/team
    # Overwrite by env SRS_TENCENTCLOUD_APM_TEAM
    team apm-xxxxxxxxx;
    # The APM token for authentication. For example, the token is xzddEaegsxGadEpGEDFx (just an example, not available),
    # please get your token from https://console.cloud.tencent.com/apm/monitor/access
    # Overwrite by env SRS_TENCENTCLOUD_APM_TOKEN
    token xxxxxxxx;
    # The APM endpoint. See https://github.com/open-telemetry/opentelemetry-go/tree/main/exporters/otlp/otlptrace
    # Please note that 4317 is for GRPC/HTTP2, while SRS only support HTTP and the port shoule be 55681.
    # Overwrite by env SRS_TENCENTCLOUD_APM_ENDPOINT
    endpoint ap-guangzhou.apm.tencentcs.com:55681;
    # The service.name of resource.
    # Overwrite by env SRS_TENCENTCLOUD_APM_SERVICE_NAME
    # Default: srs-server
    service_name srs-server;
    # Whether enable logging for each log sending.
    # Overwrite by env SRS_TENCENTCLOUD_APM_DEBUG_LOGGING
    # Default: off
    debug_logging off;
}

# Prometheus exporter config.
# See https://prometheus.io/docs/instrumenting/exporters
exporter {
    # Whether exporter is enabled.
    # Overwrite by env SRS_EXPORTER_ENABLED
    # Default: off
    enabled off;
    # The http api listen port for exporter metrics.
    # Overwrite by env SRS_EXPORTER_LISTEN
    # Default: 9972
    # See https://github.com/prometheus/prometheus/wiki/Default-port-allocations
    listen 9972;
    # The logging label to category the cluster servers.
    # Overwrite by env SRS_EXPORTER_LABEL
    label cn-beijing;
    # The logging tag to category the cluster servers.
    # Overwrite by env SRS_EXPORTER_TAG
    tag cn-edge;
}

#############################################################################################
# heartbeat/stats sections
#############################################################################################
# heartbeat to api server
# @remark, the ip report to server, is retrieve from system stat,
#       which need the config item stats.network.
heartbeat {
    # whether heartbeat is enabled.
    # Overwrite by env SRS_HEARTBEAT_ENABLED
    # default: off
    enabled off;
    # the interval seconds for heartbeat,
    # recommend 0.3,0.6,0.9,1.2,1.5,1.8,2.1,2.4,2.7,3,...,6,9,12,....
    # Overwrite by env SRS_HEARTBEAT_INTERVAL
    # default: 9.9
    interval 9.3;
    # when startup, srs will heartbeat to this api.
    # @remark: must be a restful http api url, where SRS will POST with following data:
    #   {
    #       "device_id": "my-srs-device",
    #       "ip": "*************"
    #   }
    # Overwrite by env SRS_HEARTBEAT_URL
    # default: http://127.0.0.1:8085/api/v1/servers
    url http://127.0.0.1:8085/api/v1/servers;
    # the id of device.
    # Overwrite by env SRS_HEARTBEAT_DEVICE_ID
    device_id       "my-srs-device";
    # whether report with summaries
    # if on, put /api/v1/summaries to the request data:
    #   {
    #       "summaries": summaries object.
    #   }
    # @remark: optional config.
    # Overwrite by env SRS_HEARTBEAT_SUMMARIES
    # default: off
    summaries off;
}

# system statistics section.
# the main cycle will retrieve the system stat,
# for example, the cpu/mem/network/disk-io data,
# the http api, for instance, /api/v1/summaries will show these data.
# @remark the heartbeat depends on the network,
#       for example, the eth0 maybe the device which index is 0.
stats {
    # Whether enable the stat of system resources.
    # Default: on
    enabled on;
    # the index of device ip.
    # we may retrieve more than one network device.
    # default: 0
    network 0;
    # the device name to stat the disk iops.
    # ignore the device of /proc/diskstats if not configured.
    disk sda sdb xvda xvdb;
}

#############################################################################################
# RTMP/HTTP VHOST sections
#############################################################################################
# vhost list, the __defaultVhost__ is the default vhost
# for example, user use ip to access the stream: rtmp://***********/live/livestream.
# for which cannot identify the required vhost.
vhost __defaultVhost__ {
}

# the vhost scope configs.
vhost scope.vhost.srs.com {
    # whether the vhost is enabled.
    # if off, all request access denied.
    # default: on
    enabled off;

    # whether enable min delay mode for vhost.
    # for min latency mode:
    # 1. disable the publish.mr for vhost.
    # 2. use timeout for cond wait for consumer queue.
    # @see https://github.com/ossrs/srs/issues/257
    # default: off (for RTMP/HTTP-FLV)
    # default: on (for WebRTC)
    min_latency off;

    # whether enable the TCP_NODELAY
    # if on, set the nodelay of fd by setsockopt
    # Overwrite by env SRS_VHOST_TCP_NODELAY for all vhosts.
    # default: off
    tcp_nodelay off;

    # the default chunk size is 128, max is 65536,
    # some client does not support chunk size change,
    # vhost chunk size will override the global value.
    # Overwrite by env SRS_VHOST_CHUNK_SIZE for all vhosts.
    # default: global chunk size.
    chunk_size 128;
    
    # The input ack size, 0 to not set.
    # Generally, it's set by the message from peer,
    # but for some peer(encoder), it never send message but use a different ack size.
    # We can chnage the default ack size in server-side, to send acknowledge message,
    # or the encoder maybe blocked after publishing for some time.
    # Overwrite by env SRS_VHOST_IN_ACK_SIZE for all vhosts.
    # Default: 0
    in_ack_size 0;
    
    # The output ack size, 0 to not set.
    # This is used to notify the peer(player) to send acknowledge to server.
    # Overwrite by env SRS_VHOST_OUT_ACK_SIZE for all vhosts.
    # Default: 2500000
    out_ack_size 2500000;
}

# set the chunk size of vhost.
vhost chunksize.srs.com {
    # @see scope.vhost.srs.com
    chunk_size 128;
}

# the vhost disabled.
vhost removed.srs.com {
    # @see scope.vhost.srs.com
    enabled off;
}

# vhost for stream cluster for RTMP/FLV
vhost cluster.srs.com {
    # The config for cluster.
    cluster {
        # The cluster mode, local or remote.
        #       local: It's an origin server, serve streams itself.
        #       remote: It's an edge server, fetch or push stream to origin server.
        # default: local
        mode remote;

        # For edge(mode remote), user must specifies the origin server
        # format as: <server_name|ip>[:port]
        # @remark user can specifies multiple origin for error backup, by space,
        # for example, *************:1935 *************:1935 *************:1935
        origin 127.0.0.1:1935 localhost:1935;

        # For edge(mode remote), whether open the token traverse mode,
        # if token traverse on, all connections of edge will forward to origin to check(auth),
        # it's very important for the edge to do the token auth.
        # the better way is use http callback to do the token auth by the edge,
        # but if user prefer origin check(auth), the token_traverse if better solution.
        # default: off
        token_traverse off;

        # For edge(mode remote), the vhost to transform for edge,
        # to fetch from the specified vhost at origin,
        # if not specified, use the current vhost of edge in origin, the variable [vhost].
        # default: [vhost]
        vhost same.edge.srs.com;

        # For edge(mode remote), when upnode(forward to, edge push to, edge pull from) is srs,
        # it's strongly recommend to open the debug_srs_upnode,
        # when connect to upnode, it will take the debug info,
        # for example, the id, source id, pid.
        # please see https://ossrs.net/lts/zh-cn/docs/v4/doc/log
        # default: on
        debug_srs_upnode on;

        # For origin(mode local) cluster, turn on the cluster.
        # @remark Origin cluster only supports RTMP, use Edge to transmux RTMP to FLV.
        # default: off
        # TODO: FIXME: Support reload.
        origin_cluster off;

        # For origin (mode local) cluster, the co-worker's HTTP APIs.
        # This origin will connect to co-workers and communicate with them.
        # please see https://ossrs.io/lts/en-us/docs/v4/doc/origin-cluster
        # TODO: FIXME: Support reload.
        coworkers 127.0.0.1:9091 127.0.0.1:9092;

        # The protocol to connect to origin.
        #       rtmp, Connect origin by RTMP
        #       flv, Connect origin by HTTP-FLV
        #       flvs, Connect origin by HTTPS-FLV
        # Default: rtmp
        protocol rtmp;

        # Whether follow client protocol to connect to origin.
        # @remark The FLV might use different signature(in query string) to RTMP.
        # Default: off
        follow_client off;
    }
}

# vhost for edge, edge and origin is the same vhost
vhost same.edge.srs.com {
    # @see cluster.srs.com
    cluster {
        mode remote;
        origin 127.0.0.1:1935 localhost:1935;
        token_traverse off;
    }
}

# vhost for edge, edge transform vhost to fetch from another vhost.
vhost transform.edge.srs.com {
    # @see cluster.srs.com
    cluster {
        mode remote;
        origin 127.0.0.1:1935;
        vhost same.edge.srs.com;
    }
}

# the vhost for srs debug info, whether send args in connect(tcUrl).
vhost debug.srs.com {
    # @see cluster.srs.com
    cluster {
        debug_srs_upnode on;
    }
}

# the vhost which forward publish streams.
vhost same.vhost.forward.srs.com {
    # forward stream to other servers.
    forward {
        # whether enable the forward.
        # default: off
        enabled on;
        # forward all publish stream to the specified server.
        # this used to split/forward the current stream for cluster active-standby,
        # active-active for cdn to build high available fault tolerance system.
        # format: {ip}:{port} {ip_N}:{port_N}
        destination 127.0.0.1:1936 127.0.0.1:1937;

        # when client(encoder) publish to vhost/app/stream, call the hook in creating backend forwarder.
        # the request in the POST data string is a object encode by json:
        #       {
        #           "action": "on_forward",
        #           "server_id": "vid-k21d7y2",
        #           "client_id": "9o7g1330",
        #           "ip": "127.0.0.1",
        #           "vhost": "__defaultVhost__",
        #           "app": "live",
        #           "tcUrl": "rtmp://127.0.0.1:1935/live",
        #           "stream": "livestream",
        #           "param": ""
        #       }
        # if valid, the hook must return HTTP code 200(Status OK) and response
        # an int value specifies the error code(0 corresponding to success):
        #       {
        #          "code": 0,
        #          "data": {
        #              "urls":[
        #                 "rtmp://127.0.0.1:19350/test/teststream"
        #              ]
        #          }
        #       }
        # PS: you can transform params to backend service, such as:
        #       { "param": "?forward=rtmp://127.0.0.1:19351/test/livestream" }
        #     then backend return forward's url in response.
        # if backend return empty urls, destanition is still disabled.
        # only support one api hook, format:
        #       backend http://xxx/api0
        backend http://127.0.0.1:8085/api/v1/forward;
    }
}

# the play specified configs
vhost play.srs.com {
    # for play client, both RTMP and other stream clients,
    # for instance, the HTTP FLV stream clients.
    play {
        # whether cache the last gop.
        # if on, cache the last gop and dispatch to client,
        #   to enabled fast startup for client, client play immediately.
        # if off, send the latest media data to client,
        #   client need to wait for the next Iframe to decode and show the video.
        # set to off if requires min delay;
        # set to on if requires client fast startup.
        # Overwrite by env SRS_VHOST_PLAY_GOP_CACHE for all vhosts.
        # default: on
        gop_cache off;

        # Limit the max frames in gop cache. It might cause OOM if video stream has no IDR frame, so we limit to N
        # frames by default. Note that it's the size of gop cache, including videos, audios and other messages.
        # Overwrite by env SRS_VHOST_PLAY_GOP_CACHE_MAX_FRAMES for all vhosts.
        # default: 2500
        gop_cache_max_frames 2500;

        # the max live queue length in seconds.
        # if the messages in the queue exceed the max length,
        # drop the old whole gop.
        # Overwrite by env SRS_VHOST_PLAY_QUEUE_LENGTH for all vhosts.
        # default: 30
        queue_length 10;

        # about the stream monotonically increasing:
        #   1. video timestamp is monotonically increasing,
        #   2. audio timestamp is monotonically increasing,
        #   3. video and audio timestamp is interleaved/mixed monotonically increasing.
        # it's specified by RTMP specification, @see 3. Byte Order, Alignment, and Time Format
        # however, some encoder cannot provides this feature, please set this to off to ignore time jitter.
        # the time jitter algorithm:
        #   1. full, to ensure stream start at zero, and ensure stream monotonically increasing.
        #   2. zero, only ensure stream start at zero, ignore timestamp jitter.
        #   3. off, disable the time jitter algorithm, like atc.
        # @remark for full, correct timestamp only when |delta| > 250ms.
        # @remark disabled when atc is on.
        # Overwrite by env SRS_VHOST_PLAY_TIME_JITTER for all vhosts.
        # default: full
        time_jitter full;
        # vhost for atc for hls/hds/rtmp backup.
        # generally, atc default to off, server delivery rtmp stream to client(flash) timestamp from 0.
        # when atc is on, server delivery rtmp stream by absolute time.
        # atc is used, for instance, encoder will copy stream to master and slave server,
        # server use atc to delivery stream to edge/client, where stream time from master/slave server
        # is always the same, client/tools can slice RTMP stream to HLS according to the same time,
        # if the time not the same, the HLS stream cannot slice to support system backup.
        #
        # @see http://www.adobe.com/cn/devnet/adobe-media-server/articles/varnish-sample-for-failover.html
        # @see http://www.baidu.com/#wd=hds%20hls%20atc
        #
        # @remark when atc is on, auto off the time_jitter
        # Overwrite by env SRS_VHOST_PLAY_ATC for all vhosts.
        # default: off
        atc off;
        # whether use the interleaved/mixed algorithm to correct the timestamp.
        # if on, always ensure the timestamp of audio+video is interleaved/mixed monotonically increase.
        # if off, use time_jitter to correct the timestamp if required.
        # @remark to use mix_correct, atc should on(or time_jitter should off).
        # Overwrite by env SRS_VHOST_PLAY_MIX_CORRECT for all vhosts.
        # default: off
        mix_correct off;

        # whether enable the auto atc,
        # if enabled, detect the bravo_atc="true" in onMetaData packet,
        # set atc to on if matched.
        # always ignore the onMetaData if atc_auto is off.
        # Overwrite by env SRS_VHOST_PLAY_ATC_AUTO for all vhosts.
        # default: off
        atc_auto off;

        # set the MW(merged-write) latency in ms.
        # SRS always set mw on, so we just set the latency value.
        # the latency of stream >= mw_latency + mr_latency
        # the value recomment is [300, 1800]
        # @remark For WebRTC, we enable pass-by-timestamp mode, so we ignore this config.
        # default: 350 (For RTMP/HTTP-FLV)
        # Overwrite by env SRS_VHOST_PLAY_MW_LATENCY for all vhosts.
        # default: 0 (For WebRTC)
        mw_latency 350;

        # Set the MW(merged-write) min messages.
        # default: 0 (For Real-Time, min_latency on)
        # default: 1 (For WebRTC, min_latency off)
        # default: 8 (For RTMP/HTTP-FLV, min_latency off).
        # Overwrite by env SRS_VHOST_PLAY_MW_MSGS for all vhosts.
        mw_msgs 8;

        # the minimal packets send interval in ms,
        # used to control the ndiff of stream by srs_rtmp_dump,
        # for example, some device can only accept some stream which
        # delivery packets in constant interval(not cbr).
        # @remark 0 to disable the minimal interval.
        # @remark >0 to make the srs to send message one by one.
        # @remark user can get the right packets interval in ms by srs_rtmp_dump.
        # Overwrite by env SRS_VHOST_PLAY_SEND_MIN_INTERVAL for all vhosts.
        # default: 0
        send_min_interval 10.0;
        # whether reduce the sequence header,
        # for some client which cannot got duplicated sequence header,
        # while the sequence header is not changed yet.
        # Overwrite by env SRS_VHOST_PLAY_REDUCE_SEQUENCE_HEADER for all vhosts.
        # default: off
        reduce_sequence_header on;
    }
}

# vhost for time jitter
vhost jitter.srs.com {
    # @see play.srs.com
    # to use time_jitter full, the default config.
    play {
    }
    # to use mix_correct.
    play {
        time_jitter off;
        mix_correct on;
    }
    play {
        atc on;
        mix_correct on;
    }
    # to use atc
    play {
        atc on;
    }
}

# vhost for atc.
vhost atc.srs.com {
    # @see play.srs.com
    play {
        atc on;
        atc_auto on;
    }
}

# the MR(merged-read) setting for publisher.
# the MW(merged-write) settings for player.
vhost mrw.srs.com {
    # @see scope.vhost.srs.com
    min_latency off;

    # @see play.srs.com
    play {
        mw_latency 350;
        mw_msgs 8;
    }

    # @see publish.srs.com
    publish {
        mr on;
        mr_latency 350;
    }
}

# the vhost for min delay, do not cache any stream.
vhost min.delay.com {
    # @see scope.vhost.srs.com
    min_latency on;
    # @see scope.vhost.srs.com
    tcp_nodelay on;

    # @see play.srs.com
    play {
        mw_latency 100;
        mw_msgs 4;
        gop_cache off;
        queue_length 10;
    }

    # @see publish.srs.com
    publish {
        mr off;
    }
}

# whether disable the sps parse, for the resolution of video.
vhost no.parse.sps.com {
    # @see publish.srs.com
    publish {
        parse_sps on;
    }
}

# the vhost to control the stream delivery feature
vhost stream.control.com {
    # @see scope.vhost.srs.com
    min_latency on;
    # @see scope.vhost.srs.com
    tcp_nodelay on;

    # @see play.srs.com
    play {
        mw_latency 100;
        mw_msgs 4;
        queue_length 10;
        send_min_interval 10.0;
        reduce_sequence_header on;
    }

    # @see publish.srs.com
    publish {
        mr off;
        firstpkt_timeout 20000;
        normal_timeout 7000;
    }
}

# the publish specified configs
vhost publish.srs.com {
    # the config for FMLE/Flash publisher, which push RTMP to SRS.
    publish {
        # when enabled the mr, SRS will read as large as possible.
        # Overwrite by env SRS_VHOST_PUBLISH_MR for all vhosts.
        # default: off
        mr off;
        # the latency in ms for MR(merged-read),
        # the performance+ when latency+, and memory+,
        #       memory(buffer) = latency * kbps / 8
        # for example, latency=500ms, kbps=3000kbps, each publish connection will consume
        #       memory = 500 * 3000 / 8 = 187500B = 183KB
        # when there are 2500 publisher, the total memory of SRS at least:
        #       183KB * 2500 = 446MB
        # the recommended value is [300, 2000]
        # Overwrite by env SRS_VHOST_PUBLISH_MR_LATENCY for all vhosts.
        # default: 350
        mr_latency 350;

        # the 1st packet timeout in ms for encoder.
        # Overwrite by env SRS_VHOST_PUBLISH_FIRSTPKT_TIMEOUT for all vhosts.
        # default: 20000
        firstpkt_timeout 20000;
        # the normal packet timeout in ms for encoder.
        # Overwrite by env SRS_VHOST_PUBLISH_NORMAL_TIMEOUT for all vhosts.
        # default: 5000
        normal_timeout 7000;
        # whether parse the sps when publish stream.
        # we can got the resolution of video for stat api.
        # but we may failed to cause publish failed.
        # @remark If disabled, HLS might never update the sps/pps, it depends on this.
        # Overwrite by env SRS_VHOST_PUBLISH_PARSE_SPS for all vhosts.
        # default: on
        parse_sps on;
        # When parsing SPS/PPS, whether try ANNEXB first. If not, try IBMF first, then ANNEXB.
        # Overwrite by env SRS_VHOST_PUBLISH_TRY_ANNEXB_FIRST for all vhosts.
        # default: on
        try_annexb_first on;
        # The timeout in seconds to disconnect publisher when idle, which means no players.
        # Note that 0 means no timeout or this feature is disabled.
        # Note that this feature conflicts with forward, because it disconnect the publisher stream.
        # Overwrite by env SRS_VHOST_PUBLISH_KICKOFF_FOR_IDLE for all vhosts.
        # default: 0
        kickoff_for_idle 0;
    }
}

# the vhost for anti-suck.
vhost refer.anti_suck.com {
    # refer hotlink-denial.
    refer {
        # whether enable the refer hotlink-denial.
        # default: off.
        enabled on;
        # the common refer for play and publish.
        # if the page url of client not in the refer, access denied.
        # if not specified this field, allow all.
        # default: not specified.
        all github.com github.io;
        # refer for publish clients specified.
        # the common refer is not overridden by this.
        # if not specified this field, allow all.
        # default: not specified.
        publish github.com github.io;
        # refer for play clients specified.
        # the common refer is not overridden by this.
        # if not specified this field, allow all.
        # default: not specified.
        play github.com github.io;
    }
}

# the security to allow or deny clients.
vhost security.srs.com {
    # security for host to allow or deny clients.
    security {
        # whether enable the security for vhost.
        # default: off
        enabled on;
        # the security list, each item format as:
        #       allow|deny    publish|play    all|<ip or cidr>
        # for example:
        #       allow           publish     all;
        #       deny            publish     all;
        #       allow           publish     127.0.0.1;
        #       deny            publish     127.0.0.1;
        #       allow           publish     10.0.0.0/8;
        #       deny            publish     10.0.0.0/8;
        #       allow           play        all;
        #       deny            play        all;
        #       allow           play        127.0.0.1;
        #       deny            play        127.0.0.1;
        #       allow           play        10.0.0.0/8;
        #       deny            play        10.0.0.0/8;
        # SRS apply the following simple strategies one by one:
        #       1. allow all if security disabled.
        #       2. default to deny all when security enabled.
        #       3. allow if matches allow strategy.
        #       4. deny if matches deny strategy.
        allow play all;
        allow publish all;
    }
}

# vhost for http static and flv vod stream for each vhost.
vhost http.static.srs.com {
    # http static vhost specified config
    http_static {
        # whether enabled the http static service for vhost.
        # Overwrite by env SRS_VHOST_HTTP_STATIC_ENABLED for all vhosts.
        # default: off
        enabled on;
        # the url to mount to,
        # typical mount to [vhost]/
        # the variables:
        #       [vhost] current vhost for http server.
        # @remark the [vhost] is optional, used to mount at specified vhost.
        # @remark the http of __defaultVhost__ will override the http_server section.
        # for example:
        #       mount to [vhost]/
        #           access by http://ossrs.net:8080/xxx.html
        #       mount to [vhost]/hls
        #           access by http://ossrs.net:8080/hls/xxx.html
        #       mount to /
        #           access by http://ossrs.net:8080/xxx.html
        #           or by http://*************:8080/xxx.html
        #       mount to /hls
        #           access by http://ossrs.net:8080/hls/xxx.html
        #           or by http://*************:8080/hls/xxx.html
        # @remark the port of http is specified by http_server section.
        # Overwrite by env SRS_VHOST_HTTP_STATIC_MOUNT for all vhosts.
        # default: [vhost]/
        mount [vhost]/hls;
        # main dir of vhost,
        # to delivery HTTP stream of this vhost.
        # default: ./objs/nginx/html
        # Overwrite by env SRS_VHOST_HTTP_STATIC_DIR for all vhosts.
        dir ./objs/nginx/html/hls;
    }
}

# vhost for http flv/aac/mp3 live stream for each vhost.
vhost http.remux.srs.com {
    # http flv/mp3/aac/ts stream vhost specified config
    http_remux {
        # whether enable the http live streaming service for vhost.
        # Overwrite by env SRS_VHOST_HTTP_REMUX_ENABLED for all vhosts.
        # default: off
        enabled on;
        # the fast cache for audio stream(mp3/aac),
        # to cache more audio and send to client in a time to make android(weixin) happy.
        # @remark the flv/ts stream ignore it
        # @remark 0 to disable fast cache for http audio stream.
        # Overwrite by env SRS_VHOST_HTTP_REMUX_FAST_CACHE for all vhosts.
        # default: 0
        fast_cache 30;
        # Whether drop packet if not match header. For example, there is has_audio and has video flag in FLV header, if
        # this is set to on and has_audio is false, then SRS will drop audio packets when got audio packets. Generally
        # it should work, but sometimes you might need SRS to keep packets even when FLV header is set to false.
        # See https://github.com/ossrs/srs/issues/939#issuecomment-1348740526
        # TODO: Only support HTTP-FLV stream right now.
        # Overwrite by env SRS_VHOST_HTTP_REMUX_DROP_IF_NOT_MATCH for all vhosts.
        # Default: on
        drop_if_not_match on;
        # Whether stream has audio track, used as default value for stream metadata, for example, FLV header contains
        # this flag. Sometimes you might want to force the metadata by disable guess_has_av.
        # For HTTP-FLV, use this as default value for FLV header audio flag. See https://github.com/ossrs/srs/issues/939#issuecomment-1351385460
        # For HTTP-TS, use this as default value for PMT table. See https://github.com/ossrs/srs/issues/939#issuecomment-1365086204
        # Overwrite by env SRS_VHOST_HTTP_REMUX_HAS_AUDIO for all vhosts.
        # Default: on
        has_audio on;
        # Whether stream has video track, used as default value for stream metadata, for example, FLV header contains
        # this flag. Sometimes you might want to force the metadata by disable guess_has_av.
        # For HTTP-FLV, use this as default value for FLV header video flag. See https://github.com/ossrs/srs/issues/939#issuecomment-1351385460
        # For HTTP-TS, use this as default value for PMT table. See https://github.com/ossrs/srs/issues/939#issuecomment-1365086204
        # Overwrite by env SRS_VHOST_HTTP_REMUX_HAS_VIDEO for all vhosts.
        # Default: on
        has_video on;
        # Whether guessing stream about audio or video track, used to generate the flags in, such as FLV header. If
        # guessing, depends on sequence header and frames in gop cache, so it might be incorrect especially your stream
        # is not regular. If not guessing, use the configured default value has_audio and has_video.
        # For HTTP-FLV, enable guessing for av header flag, because FLV can't change the header. See https://github.com/ossrs/srs/issues/939#issuecomment-1351385460
        # For HTTP-TS, ignore guessing because TS refresh the PMT when codec changed. See https://github.com/ossrs/srs/issues/939#issuecomment-1365086204
        # Overwrite by env SRS_VHOST_HTTP_REMUX_GUESS_HAS_AV for all vhosts.
        # Default: on
        guess_has_av on;
        # the stream mount for rtmp to remux to live streaming.
        # typical mount to [vhost]/[app]/[stream].flv
        # the variables:
        #       [vhost] current vhost for http live stream.
        #       [app] current app for http live stream.
        #       [stream] current stream for http live stream.
        # @remark the [vhost] is optional, used to mount at specified vhost.
        # the extension:
        #       .flv mount http live flv stream, use default gop cache.
        #       .ts mount http live ts stream, use default gop cache.
        #       .mp3 mount http live mp3 stream, ignore video and audio mp3 codec required.
        #       .aac mount http live aac stream, ignore video and audio aac codec required.
        # for example:
        #       mount to [vhost]/[app]/[stream].flv
        #           access by http://ossrs.net:8080/live/livestream.flv
        #       mount to /[app]/[stream].flv
        #           access by http://ossrs.net:8080/live/livestream.flv
        #           or by http://*************:8080/live/livestream.flv
        #       mount to [vhost]/[app]/[stream].mp3
        #           access by http://ossrs.net:8080/live/livestream.mp3
        #       mount to [vhost]/[app]/[stream].aac
        #           access by http://ossrs.net:8080/live/livestream.aac
        #       mount to [vhost]/[app]/[stream].ts
        #           access by http://ossrs.net:8080/live/livestream.ts
        # @remark the port of http is specified by http_server section.
        # Overwrite by env SRS_VHOST_HTTP_REMUX_MOUNT for all vhosts.
        # default: [vhost]/[app]/[stream].flv
        mount [vhost]/[app]/[stream].flv;
    }
}

# the http hook callback vhost, srs will invoke the hooks for specified events.
vhost hooks.callback.srs.com {
    http_hooks {
        # whether the http hooks enable.
        # Overwrite by env SRS_VHOST_HTTP_HOOKS_ENABLED for all vhosts.
        # default off.
        enabled on;
        # when client(encoder) publish to vhost/app/stream, call the hook,
        # the request in the POST data string is a object encode by json:
        #       {
        #           "action": "on_publish",
        #           "client_id": "9308h583",
        #           "ip": "************", "vhost": "video.test.com", "app": "live",
        #           "stream": "livestream", "param":"?token=xxx&salt=yyy", "server_id": "vid-werty",
        #           "stream_url": "video.test.com/live/livestream", "stream_id": "vid-124q9y3"
        #       }
        # if valid, the hook must return HTTP code 200(Status OK) and response
        # an int value specifies the error code(0 corresponding to success):
        #       0
        # support multiple api hooks, format:
        #       on_publish http://xxx/api0 http://xxx/api1 http://xxx/apiN
        # @remark For SRS4, the HTTPS url is supported, for example:
        #       on_publish https://xxx/api0 https://xxx/api1 https://xxx/apiN
        # Overwrite by env SRS_VHOST_HTTP_HOOKS_ON_PUBLISH for all vhosts.
        on_publish http://127.0.0.1:8085/api/v1/streams http://localhost:8085/api/v1/streams;
        # when client(encoder) stop publish to vhost/app/stream, call the hook,
        # the request in the POST data string is a object encode by json:
        #       {
        #           "action": "on_unpublish",
        #           "client_id": "9308h583",
        #           "ip": "************", "vhost": "video.test.com", "app": "live",
        #           "stream": "livestream", "param":"?token=xxx&salt=yyy", "server_id": "vid-werty",
        #           "stream_url": "video.test.com/live/livestream", "stream_id": "vid-124q9y3"
        #       }
        # if valid, the hook must return HTTP code 200(Status OK) and response
        # an int value specifies the error code(0 corresponding to success):
        #       0
        # support multiple api hooks, format:
        #       on_unpublish http://xxx/api0 http://xxx/api1 http://xxx/apiN
        # @remark For SRS4, the HTTPS url is supported, for example:
        #       on_unpublish https://xxx/api0 https://xxx/api1 https://xxx/apiN
        # Overwrite by env SRS_VHOST_HTTP_HOOKS_ON_UNPUBLISH for all vhosts.
        on_unpublish http://127.0.0.1:8085/api/v1/streams http://localhost:8085/api/v1/streams;
        # when client start to play vhost/app/stream, call the hook,
        # the request in the POST data string is a object encode by json:
        #       {
        #           "action": "on_play",
        #           "client_id": "9308h583",
        #           "ip": "************", "vhost": "video.test.com", "app": "live",
        #           "stream": "livestream", "param":"?token=xxx&salt=yyy",
        #           "pageUrl": "http://www.test.com/live.html", "server_id": "vid-werty",
        #           "stream_url": "video.test.com/live/livestream", "stream_id": "vid-124q9y3"
        #       }
        # if valid, the hook must return HTTP code 200(Status OK) and response
        # an int value specifies the error code(0 corresponding to success):
        #       0
        # support multiple api hooks, format:
        #       on_play http://xxx/api0 http://xxx/api1 http://xxx/apiN
        # @remark For SRS4, the HTTPS url is supported, for example:
        #       on_play https://xxx/api0 https://xxx/api1 https://xxx/apiN
        # Overwrite by env SRS_VHOST_HTTP_HOOKS_ON_PLAY for all vhosts.
        on_play http://127.0.0.1:8085/api/v1/sessions http://localhost:8085/api/v1/sessions;
        # when client stop to play vhost/app/stream, call the hook,
        # the request in the POST data string is a object encode by json:
        #       {
        #           "action": "on_stop",
        #           "client_id": "9308h583",
        #           "ip": "************", "vhost": "video.test.com", "app": "live",
        #           "stream": "livestream", "param":"?token=xxx&salt=yyy", "server_id": "vid-werty",
        #           "stream_url": "video.test.com/live/livestream", "stream_id": "vid-124q9y3"
        #       }
        # if valid, the hook must return HTTP code 200(Status OK) and response
        # an int value specifies the error code(0 corresponding to success):
        #       0
        # support multiple api hooks, format:
        #       on_stop http://xxx/api0 http://xxx/api1 http://xxx/apiN
        # @remark For SRS4, the HTTPS url is supported, for example:
        #       on_stop https://xxx/api0 https://xxx/api1 https://xxx/apiN
        # Overwrite by env SRS_VHOST_HTTP_HOOKS_ON_STOP for all vhosts.
        on_stop http://127.0.0.1:8085/api/v1/sessions http://localhost:8085/api/v1/sessions;
        # when srs reap a dvr file, call the hook,
        # the request in the POST data string is a object encode by json:
        #       {
        #           "action": "on_dvr",
        #           "client_id": "9308h583",
        #           "ip": "************", "vhost": "video.test.com", "app": "live",
        #           "stream": "livestream", "param":"?token=xxx&salt=yyy",
        #           "cwd": "/usr/local/srs",
        #           "file": "./objs/nginx/html/live/livestream.1420254068776.flv", "server_id": "vid-werty",
        #           "stream_url": "video.test.com/live/livestream", "stream_id": "vid-124q9y3"
        #       }
        # if valid, the hook must return HTTP code 200(Status OK) and response
        # an int value specifies the error code(0 corresponding to success):
        #       0
        # Overwrite by env SRS_VHOST_HTTP_HOOKS_ON_DVR for all vhosts.
        on_dvr http://127.0.0.1:8085/api/v1/dvrs http://localhost:8085/api/v1/dvrs;
        # when srs reap a ts file of hls, call the hook,
        # the request in the POST data string is a object encode by json:
        #       {
        #           "action": "on_hls",
        #           "client_id": "9308h583",
        #           "ip": "************", "vhost": "video.test.com", "app": "live",
        #           "stream": "livestream", "param":"?token=xxx&salt=yyy",
        #           "duration": 9.36, // in seconds
        #           "cwd": "/usr/local/srs",
        #           "file": "./objs/nginx/html/live/livestream/2015-04-23/01/476584165.ts",
        #           "url": "live/livestream/2015-04-23/01/476584165.ts",
        #           "m3u8": "./objs/nginx/html/live/livestream/live.m3u8",
        #           "m3u8_url": "live/livestream/live.m3u8",
        #           "seq_no": 100, "server_id": "vid-werty",
        #           "stream_url": "video.test.com/live/livestream", "stream_id": "vid-124q9y3"
        #       }
        # if valid, the hook must return HTTP code 200(Status OK) and response
        # an int value specifies the error code(0 corresponding to success):
        #       0
        # Overwrite by env SRS_VHOST_HTTP_HOOKS_ON_HLS for all vhosts.
        on_hls http://127.0.0.1:8085/api/v1/hls http://localhost:8085/api/v1/hls;
        # when srs reap a ts file of hls, call this hook,
        # used to push file to cdn network, by get the ts file from cdn network.
        # so we use HTTP GET and use the variable following:
        #       [server_id], replace with the server_id
        #       [app], replace with the app.
        #       [stream], replace with the stream.
        #       [param], replace with the param.
        #       [ts_url], replace with the ts url.
        # ignore any return data of server.
        # @remark random select a url to report, not report all.
        # Overwrite by env SRS_VHOST_HTTP_HOOKS_ON_HLS_NOTIFY for all vhosts.
        on_hls_notify http://127.0.0.1:8085/api/v1/hls/[server_id]/[app]/[stream]/[ts_url][param];
    }
}

# the vhost for exec, fork process when publish stream.
vhost exec.srs.com {
    # the exec used to fork process when got some event.
    exec {
        # whether enable the exec.
        # default: off.
        enabled off;
        # when publish stream, exec the process with variables:
        #       [vhost] the input stream vhost.
        #       [port] the input stream port.
        #       [app] the input stream app.
        #       [stream] the input stream name.
        #       [engine] the transcode engine name.
        # other variables for exec only:
        #       [url] the rtmp url which trigger the publish.
        #       [tcUrl] the client request tcUrl.
        #       [swfUrl] the client request swfUrl.
        #       [pageUrl] the client request pageUrl.
        # we also support datetime variables.
        #       [2006], replace this const to current year.
        #       [01], replace this const to current month.
        #       [02], replace this const to current date.
        #       [15], replace this const to current hour.
        #       [04], replace this const to current minute.
        #       [05], replace this const to current second.
        #       [999], replace this const to current millisecond.
        #       [timestamp],replace this const to current UNIX timestamp in ms.
        # @remark we use golang time format "2006-01-02 15:04:05.999" as "[2006]-[01]-[02]_[15].[04].[05]_[999]"
        # @remark empty to ignore this exec.
        publish ./objs/ffmpeg/bin/ffmpeg -f flv -i [url] -c copy -y ./[stream].flv;
    }
}

# The vhost for MPEG-DASH.
vhost dash.srs.com {
    dash {
        # Whether DASH is enabled.
        # Transmux RTMP to DASH if on.
        # Overwrite by env SRS_VHOST_DASH_ENABLED for all vhosts.
        # Default: off
        enabled on;
        # The duration of segment in seconds.
        # Overwrite by env SRS_VHOST_DASH_DASH_FRAGMENT for all vhosts.
        # Default: 30
        dash_fragment 30;
        # The period to update the MPD in seconds.
        # Overwrite by env SRS_VHOST_DASH_DASH_UPDATE_PERIOD for all vhosts.
        # Default: 150
        dash_update_period 150;
        # The depth of timeshift buffer in seconds.
        # Overwrite by env SRS_VHOST_DASH_DASH_TIMESHIFT for all vhosts.
        # Default: 300
        dash_timeshift 300;
        # The base/home dir/path for dash.
        # All init and segment files will write under this dir.
        # Overwrite by env SRS_VHOST_DASH_DASH_PATH for all vhosts.
        dash_path ./objs/nginx/html;
        # The DASH MPD file path.
        # We supports some variables to generate the filename.
        #       [vhost], the vhost of stream.
        #       [app], the app of stream.
        #       [stream], the stream name of stream.
        # Overwrite by env SRS_VHOST_DASH_DASH_MPD_FILE for all vhosts.
        # Default: [app]/[stream].mpd
        dash_mpd_file [app]/[stream].mpd;
        # The dash windows size in seconds.
        # Overwrite by env SRS_VHOST_DASH_DASH_WINDOW_SIZE for all vhosts.
        # Default: 5
        dash_window_size 5;
        # whether cleanup the old expired dash files.
        # Overwrite by env SRS_VHOST_DASH_DASH_CLEANUP for all vhosts.
        # default: on
        dash_cleanup on;
        # If there is no incoming packets, dispose DASH in this timeout in seconds,
        # which removes all DASH files including m3u8 and ts files.
        # @remark 0 to disable dispose for publisher.
        # @remark apply for publisher timeout only, while "etc/init.d/srs stop" always dispose DASH.
        # Overwrite by env SRS_VHOST_DASH_DASH_DISPOSE for all vhosts.
        # default: 120
        dash_dispose 120;
    }
}

# the vhost with hls specified.
vhost hls.srs.com {
    hls {
        # whether the hls is enabled.
        # if off, do not write hls(ts and m3u8) when publish.
        # Overwrite by env SRS_VHOST_HLS_ENABLED for all vhosts.
        # default: off
        enabled on;

        # the hls fragment in seconds, the duration of a piece of ts.
        # Overwrite by env SRS_VHOST_HLS_HLS_FRAGMENT for all vhosts.
        # default: 10
        hls_fragment 10;
        # the hls m3u8 target duration ratio,
        #   EXT-X-TARGETDURATION = hls_td_ratio * hls_fragment // init
        #   EXT-X-TARGETDURATION = max(ts_duration, EXT-X-TARGETDURATION) // for each ts
        # Overwrite by env SRS_VHOST_HLS_HLS_TD_RATIO for all vhosts.
        # default: 1.0
        hls_td_ratio 1.0;
        # the audio overflow ratio.
        # for pure audio, the duration to reap the segment.
        # for example, the hls_fragment is 10s, hls_aof_ratio is 1.2,
        # the segment will reap to 12s for pure audio.
        # Overwrite by env SRS_VHOST_HLS_HLS_AOF_RATIO for all vhosts.
        # default: 2.1
        hls_aof_ratio 2.1;
        # the hls window in seconds, the number of ts in m3u8.
        # Overwrite by env SRS_VHOST_HLS_HLS_WINDOW for all vhosts.
        # default: 60
        hls_window 60;
        # the error strategy. can be:
        #       ignore, disable the hls.
        #       disconnect, require encoder republish.
        #       continue, ignore failed try to continue output hls.
        # Overwrite by env SRS_VHOST_HLS_HLS_ON_ERROR for all vhosts.
        # default: continue
        hls_on_error continue;
        # the hls output path.
        # the m3u8 file is configured by hls_path/hls_m3u8_file, the default is:
        #       ./objs/nginx/html/[app]/[stream].m3u8
        # the ts file is configured by hls_path/hls_ts_file, the default is:
        #       ./objs/nginx/html/[app]/[stream]-[seq].ts
        # @remark the hls_path is compatible with srs v1 config.
        # Overwrite by env SRS_VHOST_HLS_HLS_PATH for all vhosts.
        # default: ./objs/nginx/html
        hls_path ./objs/nginx/html;
        # the hls m3u8 file name.
        # we supports some variables to generate the filename.
        #       [vhost], the vhost of stream.
        #       [app], the app of stream.
        #       [stream], the stream name of stream.
        # Overwrite by env SRS_VHOST_HLS_HLS_M3U8_FILE for all vhosts.
        # default: [app]/[stream].m3u8
        hls_m3u8_file [app]/[stream].m3u8;
        # the hls ts file name.
        # we supports some variables to generate the filename.
        #       [vhost], the vhost of stream.
        #       [app], the app of stream.
        #       [stream], the stream name of stream.
        #       [2006], replace this const to current year.
        #       [01], replace this const to current month.
        #       [02], replace this const to current date.
        #       [15], replace this const to current hour.
        #       [04], replace this const to current minute.
        #       [05], replace this const to current second.
        #       [999], replace this const to current millisecond.
        #       [timestamp],replace this const to current UNIX timestamp in ms.
        #       [seq], the sequence number of ts.
        #       [duration], replace this const to current ts duration.
        # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/dvr#custom-path
        # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/delivery-hls#hls-config
        # Overwrite by env SRS_VHOST_HLS_HLS_TS_FILE for all vhosts.
        # default: [app]/[stream]-[seq].ts
        hls_ts_file [app]/[stream]-[seq].ts;
        # the hls entry prefix, which is base url of ts url.
        # for example, the prefix is:
        #         http://your-server/
        # then, the ts path in m3u8 will be like:
        #         http://your-server/live/livestream-0.ts
        #         http://your-server/live/livestream-1.ts
        #         ...
        # Overwrite by env SRS_VHOST_HLS_HLS_ENTRY_PREFIX for all vhosts.
        # optional, default to empty string.
        hls_entry_prefix http://your-server;
        # the default audio codec of hls.
        # when codec changed, write the PAT/PMT table, but maybe ok util next ts.
        # so user can set the default codec for mp3.
        # the available audio codec:
        #       aac, mp3, an
        # Overwrite by env SRS_VHOST_HLS_HLS_ACODEC for all vhosts.
        # default: aac
        hls_acodec aac;
        # the default video codec of hls.
        # when codec changed, write the PAT/PMT table, but maybe ok util next ts.
        # so user can set the default codec for pure audio(without video) to vn.
        # the available video codec:
        #       h264, vn
        # Overwrite by env SRS_VHOST_HLS_HLS_VCODEC for all vhosts.
        # default: h264
        hls_vcodec h264;
        # whether cleanup the old expired ts files.
        # Overwrite by env SRS_VHOST_HLS_HLS_CLEANUP for all vhosts.
        # default: on
        hls_cleanup on;
        # If there is no incoming packets, dispose HLS in this timeout in seconds,
        # which removes all HLS files including m3u8 and ts files.
        # @remark 0 to disable dispose for publisher.
        # @remark apply for publisher timeout only, while "etc/init.d/srs stop" always dispose hls.
        # Overwrite by env SRS_VHOST_HLS_HLS_DISPOSE for all vhosts.
        # default: 120
        hls_dispose 120;
        # whether wait keyframe to reap segment,
        # if off, reap segment when duration exceed the fragment,
        # if on, reap segment when duration exceed and got keyframe.
        # Overwrite by env SRS_VHOST_HLS_HLS_WAIT_KEYFRAME for all vhosts.
        # default: on
        hls_wait_keyframe on;
        # whether use floor for the hls_ts_file path generation.
        # if on, use floor(timestamp/hls_fragment) as the variable [timestamp],
        #       and use enhanced algorithm to calc deviation for segment.
        # @remark when floor on, recommend the hls_segment>=2*gop.
        # Overwrite by env SRS_VHOST_HLS_HLS_TS_FLOOR for all vhosts.
        # default: off
        hls_ts_floor off;
        # the max size to notify hls,
        # to read max bytes from ts of specified cdn network,
        # @remark only used when on_hls_notify is config.
        # Overwrite by env SRS_VHOST_HLS_HLS_NB_NOTIFY for all vhosts.
        # default: 64
        hls_nb_notify 64;

        # Whether enable hls_ctx for HLS streaming, for which we create a "fake" connection for HTTP API and callback.
        # For each HLS streaming session, we use a child m3u8 with a session identified by query "hls_ctx", it simply
        # work as the session id.
        # Once the HLS streaming session is created, we will cleanup it when timeout in 2*hls_window seconds. So it
        # takes a long time period to identify the timeout.
        # Now we got a HLS stremaing session, just like RTMP/WebRTC/HTTP-FLV streaming, we're able to stat the session
        # as a "fake" connection, do HTTP callback when start playing the HLS streaming. You're able to do querying and
        # authentication.
        # Note that it will make NGINX edge cache always missed, so never enable HLS streaming if use NGINX edges.
        # Overwrite by env SRS_VHOST_HLS_HLS_CTX for all vhosts.
        # Default: on
        hls_ctx on;
        # For HLS pseudo streaming, whether enable the session for each TS segment.
        # If enabled, SRS HTTP API will show the statistics about HLS streaming bandwidth, both m3u8 and ts file. Please
        # note that it also consumes resource, because each ts file should be served by SRS, all NGINX cache will be
        # missed because we add session id to each ts file.
        # Note that it will make NGINX edge cache always missed, so never enable HLS streaming if use NGINX edges.
        # Overwrite by env SRS_VHOST_HLS_HLS_TS_CTX for all vhosts.
        # Default: on
        hls_ts_ctx on;

        # whether using AES encryption.
        # Overwrite by env SRS_VHOST_HLS_HLS_KEYS for all vhosts.
        # default: off
        hls_keys on;
        # the number of clear ts which one key can encrypt.
        # Overwrite by env SRS_VHOST_HLS_HLS_FRAGMENTS_PER_KEY for all vhosts.
        # default: 5
        hls_fragments_per_key 5;
        # the hls key file name.
        # we supports some variables to generate the filename.
        #       [vhost], the vhost of stream.
        #       [app], the app of stream.
        #       [stream], the stream name of stream.
        #       [seq], the sequence number of key corresponding to the ts.
        # Overwrite by env SRS_VHOST_HLS_HLS_KEY_FILE for all vhosts.
        hls_key_file [app]/[stream]-[seq].key;
        # the key output path.
        # the key file is configed by hls_path/hls_key_file, the default is:
        # ./objs/nginx/html/[app]/[stream]-[seq].key
        # Overwrite by env SRS_VHOST_HLS_HLS_KEY_FILE_PATH for all vhosts.
        hls_key_file_path ./objs/nginx/html;
        # the key root URL, use this can support https.
        # @remark It's optional.
        # Overwrite by env SRS_VHOST_HLS_HLS_KEY_URL for all vhosts.
        hls_key_url https://localhost:8080;

        # Special control controls.
        ###########################################
        # Whether calculate the DTS of audio frame directly.
        # If on, guess the specific DTS by AAC samples, please read https://github.com/ossrs/srs/issues/547#issuecomment-294350544
        # If off, directly turn the FLV timestamp to DTS, which might cause corrupt audio stream.
        # @remark Recommend to set to off, unless your audio stream sample-rate and timestamp is not correct.
        # Overwrite by env SRS_VHOST_HLS_HLS_DTS_DIRECTLY for all vhosts.
        # Default: on
        hls_dts_directly on;

        # on_hls, never config in here, should config in http_hooks.
        # for the hls http callback, @see http_hooks.on_hls of vhost hooks.callback.srs.com
        # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/delivery-hls#http-callback
        # @see https://ossrs.io/lts/en-us/docs/v4/doc/delivery-hls#http-callback

        # on_hls_notify, never config in here, should config in http_hooks.
        # we support the variables to generate the notify url:
        #       [app], replace with the app.
        #       [stream], replace with the stream.
        #       [param], replace with the param.
        #       [ts_url], replace with the ts url.
        # for the hls http callback, @see http_hooks.on_hls_notify of vhost hooks.callback.srs.com
        # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/delivery-hls#on-hls-notify
        # @see https://ossrs.io/lts/en-us/docs/v4/doc/delivery-hls#on-hls-notify
    }
}
# the vhost with hls disabled.
vhost no-hls.srs.com {
    hls {
        # whether the hls is enabled.
        # if off, do not write hls(ts and m3u8) when publish.
        # default: off
        enabled off;
    }
}

# the vhost with adobe hds
vhost hds.srs.com {
    hds {
        # whether hds enabled
        # Overwrite by env SRS_VHOST_HDS_ENABLED for all vhosts.
        # default: off
        enabled on;
        # the hds fragment in seconds.
        # Overwrite by env SRS_VHOST_HDS_HDS_FRAGMENT for all vhosts.
        # default: 10
        hds_fragment 10;
        # the hds window in seconds, erase the segment when exceed the window.
        # Overwrite by env SRS_VHOST_HDS_HDS_WINDOW for all vhosts.
        # default: 60
        hds_window 60;
        # the path to store the hds files.
        # Overwrite by env SRS_VHOST_HDS_HDS_PATH for all vhosts.
        # default: ./objs/nginx/html
        hds_path ./objs/nginx/html;
    }
}

# vhost for dvr
vhost dvr.srs.com {
    # DVR RTMP stream to file,
    # start to record to file when encoder publish,
    # reap flv/mp4 according by specified dvr_plan.
    dvr {
        # whether enabled dvr features
        # Overwrite by env SRS_VHOST_DVR_ENABLED for all vhosts.
        # default: off
        enabled on;
        # the filter for dvr to apply to.
        #       all, dvr all streams of all apps.
        #       <app>/<stream>, apply to specified stream of app.
        # for example, to dvr the following two streams:
        #       live/stream1 live/stream2
        # @remark Reload is disabled, @see https://github.com/ossrs/srs/issues/2181
        # default: all
        dvr_apply all;
        # the dvr plan. canbe:
        #       session reap flv/mp4 when session end(unpublish).
        #       segment reap flv/mp4 when flv duration exceed the specified dvr_duration.
        # @remark The plan append is removed in SRS3+, for it's no use.
        # Overwrite by env SRS_VHOST_DVR_DVR_PLAN for all vhosts.
        # default: session
        dvr_plan session;
        # the dvr output path, *.flv or *.mp4.
        # we supports some variables to generate the filename.
        #       [vhost], the vhost of stream.
        #       [app], the app of stream.
        #       [stream], the stream name of stream.
        #       [2006], replace this const to current year.
        #       [01], replace this const to current month.
        #       [02], replace this const to current date.
        #       [15], replace this const to current hour.
        #       [04], replace this const to current minute.
        #       [05], replace this const to current second.
        #       [999], replace this const to current millisecond.
        #       [timestamp],replace this const to current UNIX timestamp in ms.
        # @remark we use golang time format "2006-01-02 15:04:05.999" as "[2006]-[01]-[02]_[15].[04].[05]_[999]"
        # for example, for url rtmp://ossrs.net/live/livestream and time 2015-01-03 10:57:30.776
        # 1. No variables, the rule of SRS1.0(auto add [stream].[timestamp].flv as filename):
        #       dvr_path ./objs/nginx/html;
        #       =>
        #       dvr_path ./objs/nginx/html/live/livestream.1420254068776.flv;
        # 2. Use stream and date as dir name, time as filename:
        #       dvr_path /data/[vhost]/[app]/[stream]/[2006]/[01]/[02]/[15].[04].[05].[999].flv;
        #       =>
        #       dvr_path /data/ossrs.net/live/livestream/2015/01/03/10.57.30.776.flv;
        # 3. Use stream and year/month as dir name, date and time as filename:
        #       dvr_path /data/[vhost]/[app]/[stream]/[2006]/[01]/[02]-[15].[04].[05].[999].flv;
        #       =>
        #       dvr_path /data/ossrs.net/live/livestream/2015/01/03-10.57.30.776.flv;
        # 4. Use vhost/app and year/month as dir name, stream/date/time as filename:
        #       dvr_path /data/[vhost]/[app]/[2006]/[01]/[stream]-[02]-[15].[04].[05].[999].flv;
        #       =>
        #       dvr_path /data/ossrs.net/live/2015/01/livestream-03-10.57.30.776.flv;
        # 5. DVR to mp4:
        #       dvr_path ./objs/nginx/html/[app]/[stream].[timestamp].mp4;
        #       =>
        #       dvr_path ./objs/nginx/html/live/livestream.1420254068776.mp4;
        # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/dvr#custom-path
        # @see https://ossrs.io/lts/en-us/docs/v4/doc/dvr#custom-path
        #       segment,session apply it.
        # Overwrite by env SRS_VHOST_DVR_DVR_PATH for all vhosts.
        # default: ./objs/nginx/html/[app]/[stream].[timestamp].flv
        dvr_path ./objs/nginx/html/[app]/[stream].[timestamp].flv;
        # the duration for dvr file, reap if exceed, in seconds.
        #       segment apply it.
        #       session,append ignore.
        # Overwrite by env SRS_VHOST_DVR_DVR_DURATION for all vhosts.
        # default: 30
        dvr_duration 30;
        # whether wait keyframe to reap segment,
        # if off, reap segment when duration exceed the dvr_duration,
        # if on, reap segment when duration exceed and got keyframe.
        #       segment apply it.
        #       session,append ignore.
        # Overwrite by env SRS_VHOST_DVR_DVR_WAIT_KEYFRAME for all vhosts.
        # default: on
        dvr_wait_keyframe on;
        # about the stream monotonically increasing:
        #   1. video timestamp is monotonically increasing,
        #   2. audio timestamp is monotonically increasing,
        #   3. video and audio timestamp is interleaved monotonically increasing.
        # it's specified by RTMP specification, @see 3. Byte Order, Alignment, and Time Format
        # however, some encoder cannot provides this feature, please set this to off to ignore time jitter.
        # the time jitter algorithm:
        #   1. full, to ensure stream start at zero, and ensure stream monotonically increasing.
        #   2. zero, only ensure stream start at zero, ignore timestamp jitter.
        #   3. off, disable the time jitter algorithm, like atc.
        # apply for all dvr plan.
        # Overwrite by env SRS_VHOST_DVR_TIME_JITTER for all vhosts.
        # default: full
        time_jitter full;

        # on_dvr, never config in here, should config in http_hooks.
        # for the dvr http callback, @see http_hooks.on_dvr of vhost hooks.callback.srs.com
        # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/dvr#http-callback
        # @see https://ossrs.io/lts/en-us/docs/v4/doc/dvr#http-callback
    }
}

# vhost for ingest
vhost ingest.srs.com {
    # ingest file/stream/device then push to SRS over RTMP.
    # the name/id used to identify the ingest, must be unique in global.
    # ingest id is used in reload or http api management.
    # @remark vhost can contains multiple ingest
    ingest livestream {
        # whether enabled ingest features
        # default: off
        enabled on;
        # input file/stream/device
        # @remark only support one input.
        input {
            # the type of input.
            # can be file/stream/device, that is,
            #   file: ingest file specified by url.
            #   stream: ingest stream specified by url.
            #   device: not support yet.
            # default: file
            type file;
            # the url of file/stream.
            url ./doc/source.200kbps.768x320.flv;
        }
        # the ffmpeg
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        # the transcode engine, @see all.transcode.srs.com
        # @remark, the output is specified following.
        engine {
            # @see enabled of transcode engine.
            # if disabled or vcodec/acodec not specified, use copy.
            # default: off.
            enabled off;
            # output stream. variables:
            #       [vhost] current vhost which start the ingest.
            #       [port] system RTMP stream port.
            # we also support datetime variables.
            #       [2006], replace this const to current year.
            #       [01], replace this const to current month.
            #       [02], replace this const to current date.
            #       [15], replace this const to current hour.
            #       [04], replace this const to current minute.
            #       [05], replace this const to current second.
            #       [999], replace this const to current millisecond.
            #       [timestamp],replace this const to current UNIX timestamp in ms.
            # @remark we use golang time format "2006-01-02 15:04:05.999" as "[2006]-[01]-[02]_[15].[04].[05]_[999]"
            output rtmp://127.0.0.1:[port]/live/livestream?vhost=[vhost];
        }
    }
}

# the vhost for ingest with transcode engine.
vhost transcode.ingest.srs.com {
    ingest livestream {
        enabled on;
        input {
            type file;
            url ./doc/source.200kbps.768x320.flv;
        }
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        engine {
            enabled off;
            perfile {
                re;
                rtsp_transport tcp;
            }
            iformat flv;
            vfilter {
                i ./doc/ffmpeg-logo.png;
                filter_complex  'overlay=10:10';
            }
            vcodec libx264;
            vbitrate 1500;
            vfps 25;
            vwidth 768;
            vheight 320;
            vthreads 12;
            vprofile main;
            vpreset medium;
            vparams {
                t 100;
                coder 1;
                b_strategy 2;
                bf 3;
                refs 10;
            }
            acodec libfdk_aac;
            abitrate 70;
            asample_rate 44100;
            achannels 2;
            aparams {
                profile:a aac_low;
            }
            oformat flv;
            output rtmp://127.0.0.1:[port]/[app]/[stream]?vhost=[vhost];
        }
    }
}

# the main comments for transcode
vhost example.transcode.srs.com {
    # the streaming transcode configs.
    # @remark vhost can contains multiple transcode
    transcode {
        # whether the transcode enabled.
        # if off, donot transcode.
        # default: off.
        enabled on;
        # the ffmpeg
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        # the transcode engine for matched stream.
        # all matched stream will transcoded to the following stream.
        # the transcode set name(ie. hd) is optional and not used.
        # we will build the parameters to fork ffmpeg:
        #       ffmpeg <perfile>
        #               -i <iformat> 
        #               <vfilter> 
        #               -vcodec <vcodec> -b:v <vbitrate> -r <vfps> -s <vwidth>x<vheight> -profile:v <vprofile> -preset <vpreset>
        #               <vparams>
        #               -acodec <acodec> -b:a <abitrate> -ar <asample_rate> -ac <achannels>
        #               <aparams>
        #               -f <oformat>
        #               -y <output>
        engine example {
            # whether the engine is enabled
            # default: off.
            enabled on;
            # pre-file options, before "-i"
            perfile {
                re;
                rtsp_transport tcp;
            }
            # input format "-i", can be:
            #       off, do not specifies the format, ffmpeg will guess it.
            #       flv, for flv or RTMP stream.
            #       other format, for example, mp4/aac whatever.
            # default: flv
            iformat flv;
            # ffmpeg filters, between "-i" and "-vcodec"
            # follows the main input.
            vfilter {
                # the logo input file.
                i ./doc/ffmpeg-logo.png;
                # the ffmpeg complex filter.
                # for filters, @see: http://ffmpeg.org/ffmpeg-filters.html
                filter_complex  'overlay=10:10';
            }
            # video encoder name, "ffmpeg -vcodec"
            # can be:
            #       libx264: use h.264(libx264) video encoder.
            #       png: use png to snapshot thumbnail.
            #       copy: donot encoder the video stream, copy it.
            #       vn: disable video output.
            vcodec libx264;
            # video bitrate, in kbps, "ffmepg -b:v"
            # @remark 0 to use source video bitrate.
            # default: 0
            vbitrate 1500;
            # video framerate, "ffmepg -r"
            # @remark 0 to use source video fps.
            # default: 0
            vfps 25;
            # video width, must be even numbers, "ffmepg -s"
            # @remark 0 to use source video width.
            # default: 0
            vwidth 768;
            # video height, must be even numbers, "ffmepg -s"
            # @remark 0 to use source video height.
            # default: 0
            vheight 320;
            # the max threads for ffmpeg to used, "ffmepg -thread"
            # default: 1
            vthreads 12;
            # x264 profile, "ffmepg -profile:v"
            # @see x264 -help, can be:
            # high,main,baseline
            vprofile main;
            # x264 preset, "ffmpeg -preset"
            # @see x264 -help, can be:
            #       ultrafast,superfast,veryfast,faster,fast
            #       medium,slow,slower,veryslow,placebo
            vpreset medium;
            # other x264 or ffmpeg video params, between "-preset" and "-acodec"
            vparams {
                # ffmpeg options, @see: http://ffmpeg.org/ffmpeg.html
                t 100;
                # 264 params, @see: http://ffmpeg.org/ffmpeg-codecs.html#libx264
                coder 1;
                b_strategy 2;
                bf 3;
                refs 10;
            }
            # audio encoder name, "ffmpeg -acodec"
            # can be:
            #       libfdk_aac: use aac(libfdk_aac) audio encoder.
            #       copy: donot encoder the audio stream, copy it.
            #       an: disable audio output.
            acodec libfdk_aac;
            # audio bitrate, in kbps, "ffmpeg -b:a"
            # [16, 72] for libfdk_aac.
            # @remark 0 to use source audio bitrate.
            # default: 0
            abitrate 70;
            # audio sample rate, "ffmpeg -ar"
            # for flv/rtmp, it must be:
            #       44100,22050,11025,5512
            # @remark 0 to use source audio sample rate.
            # default: 0
            asample_rate 44100;
            # audio channel, "ffmpeg -ac"
            # 1 for mono, 2 for stereo.
            # @remark 0 to use source audio channels.
            # default: 0
            achannels 2;
            # other ffmpeg audio params, between "-ac" and "-f"/"-y"
            aparams {
                # audio params, @see: http://ffmpeg.org/ffmpeg-codecs.html#Audio-Encoders
                # @remark SRS supported aac profile for HLS is: aac_low, aac_he, aac_he_v2
                profile:a aac_low;
                bsf:a aac_adtstoasc;
            }
            # output format, "ffmpeg -f" can be:
            #       off, do not specifies the format, ffmpeg will guess it.
            #       flv, for flv or RTMP stream.
            #       image2, for vcodec png to snapshot thumbnail.
            #       other format, for example, mp4/aac whatever.
            # default: flv
            oformat flv;
            # output stream, "ffmpeg -y", variables:
            #       [vhost] the input stream vhost.
            #       [port] the input stream port.
            #       [app] the input stream app.
            #       [stream] the input stream name.
            #       [engine] the transcode engine name.
            #       [param] the input stream query string.
            # we also support datetime variables.
            #       [2006], replace this const to current year.
            #       [01], replace this const to current month.
            #       [02], replace this const to current date.
            #       [15], replace this const to current hour.
            #       [04], replace this const to current minute.
            #       [05], replace this const to current second.
            #       [999], replace this const to current millisecond.
            #       [timestamp],replace this const to current UNIX timestamp in ms.
            # @remark we use golang time format "2006-01-02 15:04:05.999" as "[2006]-[01]-[02]_[15].[04].[05]_[999]"
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
    }
}
# the mirror filter of ffmpeg, @see: http://ffmpeg.org/ffmpeg-filters.html#Filtering-Introduction
vhost mirror.transcode.srs.com {
    transcode {
        enabled on;
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        engine mirror {
            enabled on;
            vfilter {
                vf                  'split [main][tmp]; [tmp] crop=iw:ih/2:0:0, vflip [flip]; [main][flip] overlay=0:H/2';
            }
            vcodec libx264;
            vbitrate 300;
            vfps 20;
            vwidth 768;
            vheight 320;
            vthreads 2;
            vprofile baseline;
            vpreset superfast;
            vparams {
            }
            acodec libfdk_aac;
            abitrate 45;
            asample_rate 44100;
            achannels 2;
            aparams {
            }
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
    }
}
# the drawtext filter of ffmpeg, @see: http://ffmpeg.org/ffmpeg-filters.html#drawtext-1
# remark: we remove the libfreetype which always cause build failed, you must add it manual if needed.
#######################################################################################################
# the crop filter of ffmpeg, @see: http://ffmpeg.org/ffmpeg-filters.html#crop
vhost crop.transcode.srs.com {
    transcode {
        enabled on;
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        engine crop {
            enabled on;
            vfilter {
                vf                  'crop=in_w-20:in_h-160:10:80';
            }
            vcodec libx264;
            vbitrate 300;
            vfps 20;
            vwidth 768;
            vheight 320;
            vthreads 2;
            vprofile baseline;
            vpreset superfast;
            vparams {
            }
            acodec libfdk_aac;
            abitrate 45;
            asample_rate 44100;
            achannels 2;
            aparams {
            }
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
    }
}
# the logo filter of ffmpeg, @see: http://ffmpeg.org/ffmpeg-filters.html#overlay
vhost logo.transcode.srs.com {
    transcode {
        enabled on;
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        engine logo {
            enabled on;
            vfilter {
                i ./doc/ffmpeg-logo.png;
                filter_complex      'overlay=10:10';
            }
            vcodec libx264;
            vbitrate 300;
            vfps 20;
            vwidth 768;
            vheight 320;
            vthreads 2;
            vprofile baseline;
            vpreset superfast;
            vparams {
            }
            acodec libfdk_aac;
            abitrate 45;
            asample_rate 44100;
            achannels 2;
            aparams {
            }
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
    }
}
# audio transcode only.
# for example, FMLE publish audio codec in mp3, and do not support HLS output,
# we can transcode the audio to aac and copy video to the new stream with HLS.
vhost audio.transcode.srs.com {
    transcode {
        enabled on;
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        engine acodec {
            enabled on;
            vcodec copy;
            acodec libfdk_aac;
            abitrate 45;
            asample_rate 44100;
            achannels 2;
            aparams {
            }
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
    }
}
# disable video, transcode/copy audio.
# for example, publish pure audio stream.
vhost vn.transcode.srs.com {
    transcode {
        enabled on;
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        engine vn {
            enabled on;
            vcodec vn;
            acodec libfdk_aac;
            abitrate 45;
            asample_rate 44100;
            achannels 2;
            aparams {
            }
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
    }
}
# ffmpeg-copy(forward implements by ffmpeg).
# copy the video and audio to a new stream.
vhost copy.transcode.srs.com {
    transcode {
        enabled on;
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        engine copy {
            enabled on;
            vcodec copy;
            acodec copy;
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
    }
}
# forward the input stream query string to output
vhost param.transcode.srs.com {
    transcode {
        enabled on;
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        engine copy {
            enabled on;
            vcodec copy;
            acodec copy;
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine][param];
        }
    }
}
# transcode all app and stream of vhost
# the comments, read example.transcode.srs.com
vhost all.transcode.srs.com {
    transcode {
        enabled on;
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        engine ffsuper {
            enabled on;
            iformat flv;
            vfilter {
                i ./doc/ffmpeg-logo.png;
                filter_complex  'overlay=10:10';
            }
            vcodec libx264;
            vbitrate 1500;
            vfps 25;
            vwidth 768;
            vheight 320;
            vthreads 12;
            vprofile main;
            vpreset medium;
            vparams {
                t 100;
                coder 1;
                b_strategy 2;
                bf 3;
                refs 10;
            }
            acodec libfdk_aac;
            abitrate 70;
            asample_rate 44100;
            achannels 2;
            aparams {
                profile:a aac_low;
            }
            oformat flv;
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
        engine ffhd {
            enabled on;
            vcodec libx264;
            vbitrate 1200;
            vfps 25;
            vwidth 1382;
            vheight 576;
            vthreads 6;
            vprofile main;
            vpreset medium;
            vparams {
            }
            acodec libfdk_aac;
            abitrate 70;
            asample_rate 44100;
            achannels 2;
            aparams {
            }
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
        engine ffsd {
            enabled on;
            vcodec libx264;
            vbitrate 800;
            vfps 25;
            vwidth 1152;
            vheight 480;
            vthreads 4;
            vprofile main;
            vpreset fast;
            vparams {
            }
            acodec libfdk_aac;
            abitrate 60;
            asample_rate 44100;
            achannels 2;
            aparams {
            }
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
        engine fffast {
            enabled on;
            vcodec libx264;
            vbitrate 300;
            vfps 20;
            vwidth 768;
            vheight 320;
            vthreads 2;
            vprofile baseline;
            vpreset superfast;
            vparams {
            }
            acodec libfdk_aac;
            abitrate 45;
            asample_rate 44100;
            achannels 2;
            aparams {
            }
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
        engine vcopy {
            enabled on;
            vcodec copy;
            acodec libfdk_aac;
            abitrate 45;
            asample_rate 44100;
            achannels 2;
            aparams {
            }
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
        engine acopy {
            enabled on;
            vcodec libx264;
            vbitrate 300;
            vfps 20;
            vwidth 768;
            vheight 320;
            vthreads 2;
            vprofile baseline;
            vpreset superfast;
            vparams {
            }
            acodec copy;
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
        engine copy {
            enabled on;
            vcodec copy;
            acodec copy;
            output rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
    }
}
# transcode all app and stream of app
vhost app.transcode.srs.com {
    # the streaming transcode configs.
    # if app specified, transcode all streams of app.
    transcode live {
        enabled on;
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        engine {
            enabled off;
        }
    }
}
# transcode specified stream.
vhost stream.transcode.srs.com {
    # the streaming transcode configs.
    # if stream specified, transcode the matched stream.
    transcode live/livestream {
        enabled on;
        ffmpeg ./objs/ffmpeg/bin/ffmpeg;
        engine {
            enabled off;
        }
    }
}

#############################################################################################
# In the config file, the include file can be anywhere in order to enhance the readability
# of the config file and enable the reuse of part of the config file.
# When using include files, make sure that the included files themselves have the correct SRS syntax,
# that is, config directives and blocks, and then specify the paths to these files.
#
# @see https://github.com/ossrs/srs/issues/1399
#############################################################################################
include include.vhost.conf;

#############################################################################################
# The origin cluster section
#############################################################################################
http_api {
    enabled on;
    listen 9090;
}
vhost a.origin.cluster.srs.com {
    cluster {
        mode local;
        origin_cluster on;
        coworkers 127.0.0.1:9091;
    }
}

http_api {
    enabled on;
    listen 9091;
}
vhost b.origin.cluster.srs.com {
    cluster {
        mode local;
        origin_cluster on;
        coworkers 127.0.0.1:9090;
    }
}

#############################################################################################
# To prevent user to use full.conf
#############################################################################################
# To identify the full.conf
# @remark Should never use it directly, it's only a collections of all config items.
# Default: off
is_full on;
