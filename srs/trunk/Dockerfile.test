FROM ossrs/srs:ubuntu20-cache

ARG MAKEARGS
RUN echo "MAKEARGS: ${MAKEARGS}"

# https://serverfault.com/questions/949991/how-to-install-tzdata-on-a-ubuntu-docker-image
ENV DEBIAN_FRONTEND noninteractive

# For go to build and run utest.
ENV PATH $PATH:/usr/local/go/bin

RUN apt update -y && apt install -y gcc make g++ patch unzip perl git libasan5

# Build and install SRS.
COPY . /srs
WORKDIR /srs/trunk

# Note that we must enable the gcc7 or link failed.
# Please note that we must disable the ffmpeg-opus, as it negatively impacts performance. We may consider
# enabling it in the future when support for multi-threading transcoding is available.
RUN ./configure --srt=on --gb28181=on --srt=on --apm=on --h265=on --utest=on --ffmpeg-opus=off --build-cache=on
RUN make utest ${MAKEARGS}

# Build benchmark tool.
RUN cd 3rdparty/srs-bench && make ${MAKEARGS}

